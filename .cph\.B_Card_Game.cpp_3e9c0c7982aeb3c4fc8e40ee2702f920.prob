{"name": "B. Card Game", "group": "Codeforces - Codeforces Round 964 (Div. 4)", "url": "https://codeforces.com/contest/1999/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1723050605702, "input": "5\n3 8 2 6\n1 1 1 1\n10 10 2 2\n1 1 10 10\n3 8 7 2\n", "output": "2\n0\n4\n0\n2\n"}, {"id": 1723224602892, "input": "1\n1 2 1 1", "output": "4"}, {"id": 1723224953378, "input": "1\n2 2 1 2", "output": "4"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BCardGame"}}, "batch": {"id": "e0f74941-e3e4-45d3-9e42-7b80f53df869", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Card_Game.cpp"}