{"name": "<PERSON><PERSON> and Books", "group": "Codeforces - Codeforces Round 953 (Div. 2)", "url": "https://codeforces.com/contest/1978/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n2\n1 1\n4\n2 3 3 1\n5\n2 2 3 2 2\n2\n10 3\n3\n1 2 3\n", "output": "2\n4\n5\n13\n5\n", "id": 1718528877836}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AAliceAndBooks"}}, "batch": {"id": "c5ff59b4-c93b-4601-b3fc-3feaec627a07", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Alice_and_Books.cpp"}