{"name": "<PERSON><PERSON> Binary Colouring", "group": "Codeforces - Codeforces Round 948 (Div. 2)", "url": "https://codeforces.com/contest/1977/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "7\n1\n14\n24\n15\n27\n11\n19\n", "output": "1\n1\n5\n0 -1 0 0 1\n6\n0 0 0 -1 0 1\n5\n-1 0 0 0 1\n6\n-1 0 -1 0 0 1\n5\n-1 0 -1 0 1\n5\n-1 0 1 0 1\n", "id": 1717534569937}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BBinaryColouring"}}, "batch": {"id": "b625ebf5-f3ed-4eba-b2ba-7c2bae7ddf47", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Binary_Colouring.cpp"}