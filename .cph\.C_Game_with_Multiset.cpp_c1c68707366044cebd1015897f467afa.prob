{"name": "C. Game with Multiset", "group": "Codeforces - Educational Codeforces Round 160 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1913/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1500, "tests": [{"input": "5\n1 0\n1 0\n1 0\n2 3\n2 4\n", "output": "YES\nNO\n", "id": 1730529919852}, {"id": 1730529919915, "input": "7\n1 0\n1 1\n1 2\n1 10\n2 4\n2 6\n2 7\n", "output": "YES\nYES\nYES\n"}, {"id": 1730539024543, "input": "9\n1 0\n1 0\n1 1\n1 2 \n1 3\n1 3\n1 4\n1 5\n2 66\n", "output": "YES"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CGameWithMultiset"}}, "batch": {"id": "40c458dd-66b7-418e-bf68-7e3d82e13f2b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Game_with_Multiset.cpp"}