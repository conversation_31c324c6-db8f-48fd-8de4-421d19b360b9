{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 909 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1899/E", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1730608721634, "input": "5\n5\n6 4 1 2 5\n7\n4 5 3 7 8 6 2\n6\n4 3 1 2 6 4\n4\n5 2 4 2\n3\n2 2 3\n", "output": "2\n6\n-1\n-1\n0\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "EQueueSort"}}, "batch": {"id": "443b6c1c-5fe8-41b8-94e8-dc6c909c9098", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\E_Queue_Sort.cpp"}