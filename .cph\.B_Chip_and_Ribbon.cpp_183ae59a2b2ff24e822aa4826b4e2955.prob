{"name": "B. Chip and Ribbon", "group": "Codeforces - Educational Codeforces Round 158 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1901/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n4\n1 2 2 1\n5\n1 0 1 0 1\n5\n5 4 3 2 1\n1\n12\n", "output": "1\n2\n4\n11\n", "id": 1719119741010}, {"id": 1719120378856, "input": "3\n10\n7 1 5 9 0 1 7 2 7 8\n10\n1 0 0 0 1 0 1 0 1 0\n10\n6 4 2 5 9 1 7 3 2 9", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BChipAndRibbon"}}, "batch": {"id": "3b55526d-3228-42c5-aaa6-fb163b20e799", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Chip_and_Ribbon.cpp"}