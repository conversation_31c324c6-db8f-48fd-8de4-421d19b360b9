{"name": "C. Heavy Intervals", "group": "Codeforces - Pinely Round 3 (Div. 1 + Div. 2)", "url": "https://codeforces.com/problemset/problem/1909/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "2\n2\n8 3\n12 23\n100 100\n4\n20 1 2 5\n30 4 3 10\n2 3 2 3\n", "output": "2400\n42\n", "id": 1737035262977}, {"id": 1737036536750, "input": "1\n10\n76042 155685 62534 162770 779 3495 97453 122787 86743 142857\n81292 172088 146526 199306 36432 165338 168285 127772 119677 151891\n1 8 2 9 1 1 1 4 7 4", "output": "609373"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CHeavyIntervals"}}, "batch": {"id": "fe1c1631-1095-417c-b79a-86b5c3380bc8", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Heavy_Intervals.cpp"}