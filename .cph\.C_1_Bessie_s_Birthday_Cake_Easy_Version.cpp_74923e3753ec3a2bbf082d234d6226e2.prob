{"name": "C1. <PERSON><PERSON>'s Birthday Cake (Easy Version)", "group": "Codeforces - CodeTON Round 8 (Div. 1 + Div. 2, Rated, Prizes!)", "url": "https://codeforces.com/problemset/problem/1942/C1", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730184136634, "input": "3\n8 4 0\n1 6 2 5\n8 8 0\n1 3 2 5 4 6 7 8\n4 2 0\n1 3\n", "output": "2\n6\n2\n"}, {"id": 1730184547859, "input": "1\n10 7 0\n2 4 5 6 7 8 10", "output": "8"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "C1BessiesBirthdayCakeEasyVersion"}}, "batch": {"id": "95291808-5c46-4e0c-a51a-bc3380e5279d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_1_<PERSON><PERSON>_s_Birthday_Cake_Easy_Version.cpp"}