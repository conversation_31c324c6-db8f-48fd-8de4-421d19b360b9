{"name": "<PERSON><PERSON> Tenzing and Books", "group": "Codeforces - CodeTON Round 5 (Div. 1 + Div. 2, Rated, Prizes!)", "url": "https://codeforces.com/problemset/problem/1842/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719670034821, "input": "3\n5 7\n1 2 3 4 5\n5 4 3 2 1\n1 3 5 7 9\n5 2\n3 2 3 4 5\n5 4 3 2 1\n3 3 5 7 9\n3 0\n1 2 3\n3 2 1\n2 2 2\n", "output": "Yes\nNo\nYes\n"}, {"id": 1719672274730, "input": "1\n5 18\n1 2 3 4 5\n5 4 3 2 1\n1 3 5 7 9", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BTenzingAndBooks"}}, "batch": {"id": "b67d191d-3a54-4b16-8f70-2ff9c1f1f3de", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Tenzing_and_Books.cpp"}