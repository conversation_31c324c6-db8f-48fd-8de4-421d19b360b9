{"name": "D. <PERSON> and Equalize", "group": "Codeforces - Codeforces Round 903 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1881/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730732601479, "input": "7\n5\n100 2 50 10 1\n3\n1 1 1\n4\n8 2 4 2\n4\n30 50 27 20\n2\n75 40\n2\n4 4\n3\n2 3 1\n", "output": "YES\nYES\nNO\nYES\nNO\nYES\nNO\n"}, {"id": 1730816981948, "input": "1\n5\n100 2 50 10 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DDivideAndEqualize"}}, "batch": {"id": "b27589b6-bfe7-4774-becb-32e80830f8ff", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Divide_and_Equalize.cpp"}