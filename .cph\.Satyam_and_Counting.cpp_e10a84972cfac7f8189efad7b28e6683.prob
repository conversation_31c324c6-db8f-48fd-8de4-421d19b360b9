{"name": "<PERSON><PERSON><PERSON> and <PERSON>ing", "group": "Codeforces", "url": "https://m2.codeforces.com/contest/2009/problem/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1725375752769, "input": "3\n5\n1 0\n1 1\n3 0\n5 0\n2 1\n3\n0 0\n1 0\n3 0\n9\n1 0\n2 0\n3 0\n4 0\n5 0\n2 1\n7 1\n8 1\n9 1\n", "output": "4\n0\n8\n"}, {"id": 1725377384535, "input": "1\n9\n1 0\n2 0\n3 0\n4 0\n5 0\n2 1\n7 1\n8 1\n9 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "SatyamAndCounting"}}, "batch": {"id": "007a5823-c686-4cbd-8248-9c82282617d0", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\Satyam_and_Counting.cpp"}