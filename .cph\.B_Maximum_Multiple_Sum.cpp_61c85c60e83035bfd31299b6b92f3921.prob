{"name": "<PERSON>. Maximum Multiple Sum", "group": "Codeforces - Codeforces Round 952 (Div. 4)", "url": "https://codeforces.com/contest/1985/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "2\n3\n15\n", "output": "3\n2\n", "id": 1718116833803}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMaximumMultipleSum"}}, "batch": {"id": "9caa3518-1d20-4503-9644-30ef9e811dc7", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Maximum_Multiple_Sum.cpp"}