{"name": "<PERSON>. Find the Permutation", "group": "Codeforces - Codeforces Round 997 (Div. 2)", "url": "https://codeforces.com/contest/2056/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1500, "tests": [{"input": "3\n1\n0\n5\n00101\n00101\n11001\n00001\n11110\n6\n000000\n000000\n000000\n000000\n000000\n000000\n", "output": "1\n4 2 1 3 5\n6 5 4 3 2 1\n", "id": 1737128021285}, {"id": 1737128344995, "input": "1\n5\n00101\n00101\n11001\n00001\n11110", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BFindThePermutation"}}, "batch": {"id": "377dfc9a-612a-4187-9bc6-fb4854c8c172", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Find_the_Permutation.cpp"}