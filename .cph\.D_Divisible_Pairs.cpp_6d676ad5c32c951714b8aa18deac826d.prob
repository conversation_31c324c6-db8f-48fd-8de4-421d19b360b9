{"name": "D. Divisible Pairs", "group": "Codeforces - Codeforces Round 925 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1931/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "7\n6 5 2\n1 2 7 4 9 6\n7 9 5\n1 10 15 3 8 12 15\n9 4 10\n14 10 2 2 11 11 13 5 6\n9 5 6\n10 7 6 7 9 7 7 10 10\n9 6 2\n4 9 7 1 2 2 13 3 15\n9 2 3\n14 6 1 15 12 15 8 2 15\n10 5 7\n13 3 3 2 12 11 3 7 13 14\n", "output": "2\n0\n1\n3\n5\n7\n0\n", "id": 1730432235905}, {"id": 1730432657998, "input": "1\n9 4 10\n14 10 2 2 11 11 13 5 6", "output": "1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DDivisiblePairs"}}, "batch": {"id": "9b3387b0-89c8-4235-90da-5475cee886e8", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Divisible_Pairs.cpp"}