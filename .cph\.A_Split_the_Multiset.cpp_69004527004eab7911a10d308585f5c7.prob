{"name": "<PERSON><PERSON> the Multiset", "group": "Codeforces - Codeforces Round 958 (Div. 2)", "url": "https://codeforces.com/contest/1988/problem/0", "interactive": false, "memoryLimit": 512, "timeLimit": 1000, "tests": [{"input": "4\n1 5\n5 2\n6 3\n16 4\n", "output": "0\n4\n3\n5\n", "id": 1721054291093}, {"id": 1721054814231, "input": "1\n62 5", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ASplitTheMultiset"}}, "batch": {"id": "c88aa91d-55c9-4437-b004-221e6cf50660", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Split_the_Multiset.cpp"}