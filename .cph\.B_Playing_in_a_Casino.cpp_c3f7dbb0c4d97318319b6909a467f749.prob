{"name": "B. Playing in a Casino", "group": "Codeforces - Codeforces Round 861 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1808/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "3\n3 5\n1 4 2 8 5\n7 9 2 1 4\n3 8 5 3 1\n1 4\n4 15 1 10\n4 3\n1 2 3\n3 2 1\n1 2 1\n4 2 7\n", "output": "50\n0\n31\n", "id": 1728281884767}, {"id": 1728283545156, "input": "1\n3 5\n1 4 2 8 5\n7 9 2 1 4\n3 8 5 3 1", "output": "50"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BPlayingInACasino"}}, "batch": {"id": "5f3bceec-264d-4637-98b7-db40d87e42fc", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Playing_in_a_Casino.cpp"}