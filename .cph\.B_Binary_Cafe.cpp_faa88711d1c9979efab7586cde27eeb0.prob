{"name": "B. Binary Cafe", "group": "Codeforces - Codeforces Round 878 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1840/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719673464828, "input": "5\n1 2\n2 1\n2 2\n10 2\n179 100\n", "output": "2\n2\n3\n4\n180\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BBinaryCafe"}}, "batch": {"id": "fb2db846-d147-4c47-a822-c33a181c84d4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Binary_Cafe.cpp"}