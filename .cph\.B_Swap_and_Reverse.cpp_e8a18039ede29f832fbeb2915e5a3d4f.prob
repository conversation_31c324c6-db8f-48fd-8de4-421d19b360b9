{"name": "<PERSON><PERSON> and Reverse", "group": "Codeforces - Harbour.Space Scholarship Contest 2023-2024 (Div. 1 + Div. 2)", "url": "https://codeforces.com/problemset/problem/1864/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719383949918, "input": "5\n4 2\nnima\n5 3\npanda\n9 2\ntheforces\n7 3\namirfar\n6 4\nrounds\n", "output": "aimn\naandp\nceefhorst\na<PERSON><PERSON>r\ndnorsu\n"}, {"id": 1719423343686, "input": "1\n2 1\naa", "output": "aa"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BSwapAndReverse"}}, "batch": {"id": "45c29fb8-7f7e-4a9a-9a3d-00e12f70f28c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Swap_and_Reverse.cpp"}