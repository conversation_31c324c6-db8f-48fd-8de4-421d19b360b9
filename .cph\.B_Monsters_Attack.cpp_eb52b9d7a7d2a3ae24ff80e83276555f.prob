{"name": "B. Monsters Attack!", "group": "Codeforces - Educational Codeforces Round 162 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1923/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2500, "tests": [{"id": 1718513560954, "input": "5\n3 2\n1 2 3\n-1 2 3\n2 1\n1 1\n-1 1\n4 10\n3 4 2 5\n-3 -2 1 3\n5 3\n2 1 3 2 5\n-3 -2 3 4 5\n2 1\n1 2\n1 2\n", "output": "YES\nNO\nYES\nYES\nNO\n"}, {"id": 1718518022281, "input": "1\n4 2\n1 2 3 1\n4 3 3 4", "output": "YES"}, {"id": 1718520771068, "input": "1\n3 1\n1 1 2\n2 2 3", "output": "NO"}, {"id": 1718521660994, "input": "1\n3 2\n1 1 3\n1 1 2", "output": "NO"}, {"id": 1718523466357, "input": "1\n4 2\n1 3 3 1\n4 3 3 4", "output": "YES"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMonstersAttack"}}, "batch": {"id": "a6db442b-8f0f-4852-b0e1-a2753d29f3a4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Monsters_Attack.cpp"}