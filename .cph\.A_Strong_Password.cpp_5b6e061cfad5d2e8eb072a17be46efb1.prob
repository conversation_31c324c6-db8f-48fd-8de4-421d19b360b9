{"name": "<PERSON><PERSON> Password", "group": "Codeforces - Educational Codeforces Round 168 (Rated for Div. 2)", "url": "https://codeforces.com/contest/1997/problem/0", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1722350223968, "input": "4\na\naaa\nabb\npassword\n", "output": "wa\naada\nabcb\npastsword\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AStrongPassword"}}, "batch": {"id": "2376f758-92cf-432e-83f7-6263d59ec88d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Strong_Password.cpp"}