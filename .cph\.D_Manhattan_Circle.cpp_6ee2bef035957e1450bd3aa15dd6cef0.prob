{"name": "D. Manhattan Circle", "group": "Codeforces - Codeforces Round 952 (Div. 4)", "url": "https://codeforces.com/contest/1985/problem/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "6\n5 5\n.....\n.....\n..#..\n.....\n.....\n5 5\n..#..\n.###.\n#####\n.###.\n..#..\n5 6\n......\n......\n.#....\n###...\n.#....\n1 1\n#\n5 6\n...#..\n..###.\n.#####\n..###.\n...#..\n2 10\n..........\n...#......\n", "output": "3 3\n3 3\n4 2\n1 1\n3 4\n2 4\n", "id": 1718117978785}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DManhattanCircle"}}, "batch": {"id": "585e0f8f-a970-45be-a96d-8f6061f42074", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Manhattan_Circle.cpp"}