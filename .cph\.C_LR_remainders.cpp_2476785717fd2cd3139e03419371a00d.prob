{"name": "C. LR-remainders", "group": "Codeforces - Codeforces Round 927 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1932/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n4 6\n3 1 4 2\nLRRL\n5 1\n1 1 1 1 1\nLLLLL\n6 8\n1 2 3 4 5 6\nRLLLRR\n1 10000\n10000\nR\n", "output": "0 2 4 1\n0 0 0 0 0\n0 0 0 4 4 4\n0\n", "id": 1736250880428}, {"id": 1736253566611, "input": "1\n6 8\n1 2 3 4 5 6\nRLLLRR", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CLRRemainders"}}, "batch": {"id": "418c6d6a-66bb-478b-a6d9-4d0eb479ce4e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_LR_remainders.cpp"}