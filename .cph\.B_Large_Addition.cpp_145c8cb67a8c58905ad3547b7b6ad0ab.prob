{"name": "B. Large Addition", "group": "Codeforces - Codeforces Global Round 26", "url": "https://codeforces.com/problemset/problem/1984/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1718102089159, "input": "11\n1337\n200\n1393938\n1434\n98765432123456789\n11111111111111111\n420\n1984\n10\n69\n119\n", "output": "YES\nNO\nYES\nYES\nNO\nYES\nNO\nYES\nYES\nNO\nNO\n"}, {"id": 1718106024943, "input": "1\n198929466522575070", "output": "NO"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BLargeAddition"}}, "batch": {"id": "f6598f63-d308-4508-8fab-cdad6dfbaa89", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Large_Addition.cpp"}