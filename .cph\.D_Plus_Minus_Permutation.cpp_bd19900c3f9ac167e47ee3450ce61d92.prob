{"name": "D. Plus Minus Permutation", "group": "Codeforces - Codeforces Round 895 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1872/D", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1720630445472, "input": "8\n7 2 3\n12 6 3\n9 1 9\n2 2 2\n100 20 50\n24 4 6\n1000000000 5575 25450\n4 4 1\n", "output": "12\n-3\n44\n0\n393\n87\n179179179436104\n-6\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DPlusMinusPermutation"}}, "batch": {"id": "3d1a67e6-3b0f-4db2-b8c6-b19d560634f5", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Plus_Minus_Permutation.cpp"}