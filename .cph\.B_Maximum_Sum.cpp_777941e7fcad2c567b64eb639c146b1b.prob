{"name": "B. Maximum Sum", "group": "Codeforces - Codeforces Round 936 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1946/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1717812348841, "input": "12\n2 2\n-4 -7\n3 3\n2 2 8\n1 7\n7\n5 1\n4 -2 8 -12 9\n7 4\n8 14 -9 6 0 -1 3\n7 100\n5 3 -8 12 -5 -9 3\n6 1000\n-1000000000 -1000000000 -1000000000 -1000000000 -1000000000 -1000000000\n2 1\n1000000000 8\n5 4\n0 0 0 0 0\n6 10\n48973 757292 58277 -38574 27475 999984\n7 1\n-1000 1000 -1000 1000 -1000 1000 -1000\n10 10050\n408293874 -3498597 7374783 295774930 -48574034 26623784 498754833 -294875830 283045804 85938045\n", "output": "999999996\n96\n896\n17\n351\n716455332\n42\n2\n0\n897909241\n0\n416571966\n"}, {"id": 1717817614174, "input": "1\n7 100\n5 3 -8 12 -5 -9 3", "output": "976371284\n716455332"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMaximumSum"}}, "batch": {"id": "e7c78521-546d-4cbe-80eb-84eaf3b6d416", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Maximum_Sum.cpp"}