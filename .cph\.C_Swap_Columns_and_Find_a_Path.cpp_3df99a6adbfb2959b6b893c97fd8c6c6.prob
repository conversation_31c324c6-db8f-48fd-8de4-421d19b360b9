{"name": "<PERSON><PERSON> Columns and Find a Path", "group": "Codeforces - Codeforces Round 990 (Div. 2)", "url": "https://codeforces.com/contest/2047/problem/C", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"input": "3\n1\n-10\n5\n3\n1 2 3\n10 -5 -3\n4\n2 8 5 3\n1 10 3 4\n", "output": "-5\n16\n29\n", "id": 1733209605741}, {"id": 1733211715018, "input": "1\n3\n-10 -2 1\n10 3 -1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CSwapColumnsAndFindAPath"}}, "batch": {"id": "2e803d78-2d8c-4c77-9c12-536935dfc6be", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Swap_Columns_and_Find_a_Path.cpp"}