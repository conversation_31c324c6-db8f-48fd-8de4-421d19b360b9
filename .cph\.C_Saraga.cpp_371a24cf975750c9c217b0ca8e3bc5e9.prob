{"name": "<PERSON><PERSON>", "group": "Codeforces - 2024-2025 ICPC Asia Jakarta Regional Contest (Unrated, Online Mirror, ICPC Rules, Teams Preferred)", "url": "https://codeforces.com/problemset/problem/2045/C", "interactive": false, "memoryLimit": 1024, "timeLimit": 1000, "tests": [{"input": "sarana\nolahraga\n", "output": "saga\n", "id": 1733544103228}, {"input": "ber<PERSON>ber\nwortel<PERSON><PERSON>u\n", "output": "<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "id": 1733544103178}, {"input": "icpc\nicpc\n", "output": "icpc\n", "id": 1733544103230}, {"id": 1733544103235, "input": "icpc\njakarta\n", "output": "-1\n"}, {"id": 1733545231523, "input": "gg\ngg", "output": "ggg"}, {"id": 1733545410417, "input": "ab\nab", "output": "-1"}, {"id": 1733545731578, "input": "bbrgh\nnsdbt", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CSaraga"}}, "batch": {"id": "36c9e86b-32fa-448a-87b1-42386a1bccee", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Saraga.cpp"}