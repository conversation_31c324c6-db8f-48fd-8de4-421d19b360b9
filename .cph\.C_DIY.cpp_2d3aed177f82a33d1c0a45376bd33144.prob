{"name": "C. DIY", "group": "Codeforces - 2024-2025 ICPC, NERC, Southern and Volga Russian Regional Contest (Unrated, Online Mirror, ICPC Rules, Preferably Teams)", "url": "https://codeforces.com/problemset/problem/2038/C", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"input": "3\n16\n-5 1 1 2 2 3 3 4 4 5 5 6 6 7 7 10\n8\n0 0 -1 2 2 1 1 3\n8\n0 0 0 0 0 5 0 5\n", "output": "YES\n1 2 1 7 6 2 6 7\nNO\nYES\n0 0 0 5 0 0 0 5\n", "id": 1733548535431}, {"id": 1733549987792, "input": "1\n8\n0 1 -1 -1 -1 1 0 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CDIY"}}, "batch": {"id": "48bfb0e1-4ec5-40d7-893f-0b0c24321fbf", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_DIY.cpp"}