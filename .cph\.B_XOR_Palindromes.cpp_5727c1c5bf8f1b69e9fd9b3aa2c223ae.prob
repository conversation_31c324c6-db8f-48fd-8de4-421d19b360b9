{"name": "B. XOR Palindromes", "group": "Codeforces - Codeforces Round 897 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1867/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719382940830, "input": "5\n6\n101011\n5\n00000\n9\n100100011\n3\n100\n1\n1\n", "output": "0010100\n111111\n0011111100\n0110\n11\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BXORPalindromes"}}, "batch": {"id": "317156b3-8ba5-4e0d-b15d-ad786a5ba8c5", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_XOR_Palindromes.cpp"}