{"name": "D. Digital string maximization", "group": "Codeforces - Codeforces Round 991 (Div. 3)", "url": "https://codeforces.com/contest/2050/problem/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "6\n19\n1709\n11555\n51476\n9876543210\n5891917899\n", "output": "81\n6710\n33311\n55431\n9876543210\n7875567711\n", "id": 1733412573132}, {"id": 1733413739785, "input": "1\n11555", "output": "55431"}, {"id": 1733416428079, "input": "1\n1709", "output": "6710"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DDigitalStringMaximization"}}, "batch": {"id": "04b9ea52-c16f-45cc-b8cc-30f24cae5978", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Digital_string_maximization.cpp"}