{"name": "C. XOR-distance", "group": "Codeforces - Codeforces Round 922 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1918/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "10\n4 6 0\n0 3 2\n9 6 10\n92 256 23\n165 839 201\n1 14 5\n2 7 2\n96549 34359 13851\n853686404475946 283666553522252166 127929199446003072\n735268590557942972 916721749674600979 895150420120690183\n", "output": "2\n1\n1\n164\n542\n5\n3\n37102\n27934920819538516\n104449824168870225\n", "id": 1736999645350}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CXORDistance"}}, "batch": {"id": "fec6d975-7e75-42ee-bfdc-17fc3c32fc9b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_XOR_distance.cpp"}