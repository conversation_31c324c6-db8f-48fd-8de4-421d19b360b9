{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 876 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1839/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719678223190, "input": "4\n4\n2 2\n1 6\n1 10\n1 13\n5\n3 4\n3 1\n2 5\n3 2\n3 3\n6\n1 2\n3 4\n1 4\n3 4\n3 5\n2 3\n1\n1 1\n", "output": "15\n14\n20\n1\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BLamps"}}, "batch": {"id": "8d5c7aa5-0995-415c-9fdb-a0e3c0a9195b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Lamps.cpp"}