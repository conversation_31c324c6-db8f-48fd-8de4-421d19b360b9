{"name": "B. Increase/Decrease/Copy", "group": "Codeforces - Educational Codeforces Round 166 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1976/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1717957426190, "input": "3\n1\n2\n1 3\n2\n3 3\n3 3 3\n4\n4 2 1 2\n2 1 5 2 3\n", "output": "3\n1\n8\n"}, {"id": 1717958441334, "input": "1\n4\n4 2 1 2\n2 1 5 2 3", "output": "8"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BIncreaseDecreaseCopy"}}, "batch": {"id": "7e96b3dd-bbde-40dc-8008-5ce42e136a14", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Increase_Decrease_Copy.cpp"}