{"name": "<PERSON>. Everyone Loves Tres", "group": "Codeforces - Codeforces Global Round 27", "url": "https://codeforces.com/contest/2035/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "6\n1\n2\n3\n4\n5\n7\n", "output": "-1\n66\n-1\n3366\n36366\n3336366\n", "id": 1730040726299}, {"id": 1730041044559, "input": "1\n8", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BEveryoneLovesTres"}}, "batch": {"id": "d20cac4e-7683-4252-83af-21671e95c706", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Everyone_Loves_Tres.cpp"}