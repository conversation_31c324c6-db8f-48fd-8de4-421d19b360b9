{"name": "<PERSON><PERSON> Light Switches", "group": "Codeforces - Codeforces Round 963 (Div. 2)", "url": "https://codeforces.com/contest/1993/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1722784735294, "input": "6\n4 4\n2 3 4 5\n4 3\n3 4 8 9\n3 3\n6 2 1\n1 1\n1\n7 5\n14 34 6 25 46 7 17\n6 5\n40 80 99 60 90 50\n\n", "output": "5\n10\n8\n1\n47\n100\n"}, {"id": 1722785786375, "input": "9\n4 4\n2 3 4 5\n4 3\n2 3 4 5\n4 3\n3 4 8 9\n3 3\n6 2 1\n1 1\n1\n7 5\n14 34 6 25 46 7 17\n6 5\n40 80 99 60 90 50\n6 5\n64 40 50 68 70 10\n2 1\n1 1000000000\n", "output": "5\n-1\n10\n8\n1\n47\n100\n-1\n-1\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CLightSwitches"}}, "batch": {"id": "5c388fa4-643f-439d-bdce-fa326d643dec", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Light_Switches.cpp"}