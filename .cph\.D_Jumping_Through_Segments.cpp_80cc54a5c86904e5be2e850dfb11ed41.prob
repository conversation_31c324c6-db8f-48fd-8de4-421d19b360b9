{"name": "<PERSON><PERSON> Jumping Through Segments", "group": "Codeforces - Codeforces Round 913 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1907/D", "interactive": false, "memoryLimit": 256, "timeLimit": 5000, "tests": [{"input": "4\n5\n1 5\n3 4\n5 6\n8 10\n0 1\n3\n0 2\n0 1\n0 3\n3\n3 8\n10 18\n6 11\n4\n10 20\n0 5\n15 17\n2 2\n", "output": "7\n0\n5\n13\n", "id": 1737051363534}, {"id": 1737051425472, "input": "1\n3\n3 8\n10 18\n6 11", "output": "5"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DJumpingThroughSegments"}}, "batch": {"id": "9a7a2a40-3256-401d-ae1b-103df1d81414", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Jumping_Through_Segments.cpp"}