{"name": "<PERSON><PERSON>, Fox and the Lonely Array", "group": "Codeforces - Codeforces Round 945 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1973/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "7\n1\n0\n3\n2 2 2\n3\n1 0 2\n5\n3 0 1 4 2\n5\n2 0 4 0 2\n7\n0 0 0 0 1 2 4\n8\n0 1 3 2 2 1 0 3\n", "output": "1\n1\n3\n4\n4\n7\n3\n", "id": 1728897835210}, {"id": 1728898373257, "input": "1\n8\n0 1 3 2 2 1 0 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BCatFoxAndTheLonelyArray"}}, "batch": {"id": "4aac67ca-154a-4142-8ec0-21ae60a7ca37", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Cat_Fox_and_the_Lonely_Array.cpp"}