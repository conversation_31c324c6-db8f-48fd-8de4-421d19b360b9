{"name": "<PERSON><PERSON> Mathematical Problem", "group": "Codeforces - Codeforces Round 954 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1986/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1734246464768, "input": "18\n2\n10\n2\n74\n2\n00\n2\n01\n3\n901\n3\n101\n5\n23311\n6\n987009\n7\n1111111\n20\n99999999999999999999\n20\n00000000000000000000\n4\n0212\n18\n057235283621345395\n4\n1112\n20\n19811678487321784121\n4\n1121\n4\n2221\n3\n011\n", "output": "10\n74\n0\n1\n9\n1\n19\n0\n11\n261\n0\n0\n0\n12\n93\n12\n24\n0\n"}, {"id": 1734249723954, "input": "1\n3\n901", "output": "9"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DMathematicalProblem"}}, "batch": {"id": "33ad9e66-0bd7-4c24-845c-be3f9bba5b12", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Mathematical_Problem.cpp"}