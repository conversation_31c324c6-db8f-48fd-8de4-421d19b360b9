{"name": "B. Minimize Equal Sum Subarrays", "group": "Codeforces - Codeforces Round 965 (Div. 2)", "url": "https://codeforces.com/contest/1998/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1500, "tests": [{"input": "3\n2\n1 2\n5\n1 2 3 4 5\n7\n4 7 5 1 2 6 3\n", "output": "2 1\n3 5 4 2 1\n6 2 1 4 7 3 5\n", "id": 1723301834539}, {"id": 1723302323656, "input": "1\n5\n5 2 4 1 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMinimizeEqualSumSubarrays"}}, "batch": {"id": "30c066c0-43e3-4372-8e89-73515d38c24e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Minimize_Equal_Sum_Subarrays.cpp"}