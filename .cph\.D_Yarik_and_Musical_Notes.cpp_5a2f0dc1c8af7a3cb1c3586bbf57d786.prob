{"name": "<PERSON><PERSON> and Musical Notes", "group": "Codeforces - Codeforces Round 909 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1899/D", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n1\n2\n4\n3 1 3 2\n2\n1000 1000\n3\n1 1 1\n19\n2 4 1 6 2 8 5 4 2 10 5 10 8 7 4 3 2 6 10\n", "output": "0\n2\n1\n3\n19\n", "id": 1730692976717}, {"id": 1730693254271, "input": "1\n19\n2 4 1 6 2 8 5 4 2 10 5 10 8 7 4 3 2 6 10", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DYarikAndMusicalNotes"}}, "batch": {"id": "e2b89d86-cdb8-40f1-af5e-28a01c2d5b7a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Yarik_and_Musical_Notes.cpp"}