{"name": "<PERSON><PERSON> Items", "group": "Codeforces - Educational Codeforces Round 169 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2004/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1723735228930, "input": "4\n2 5\n1 10\n3 0\n10 15 12\n4 6\n3 1 2 4\n2 4\n6 9\n", "output": "4\n13\n0\n0\n"}, {"id": 1723735741165, "input": "1\n2 5\n1 10", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CSplittingItems"}}, "batch": {"id": "7d40f6e9-172e-4c1f-94e5-e1b70777505d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Splitting_Items.cpp"}