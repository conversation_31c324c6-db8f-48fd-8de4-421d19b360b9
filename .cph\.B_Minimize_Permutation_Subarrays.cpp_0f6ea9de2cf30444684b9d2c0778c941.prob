{"name": "B. Minimize Permutation Subarrays", "group": "Codeforces - Codeforces Round 877 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1838/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719679726751, "input": "8\n3\n1 2 3\n3\n1 3 2\n5\n1 3 2 5 4\n6\n4 5 6 1 2 3\n9\n8 7 6 3 2 1 4 5 9\n10\n7 10 5 1 9 8 3 2 6 4\n10\n8 5 10 9 2 1 3 4 6 7\n10\n2 3 5 7 10 1 8 6 4 9\n", "output": "2 3\n1 1\n5 2\n1 4\n9 5\n8 8\n6 10\n5 4\n"}, {"id": 1719686619393, "input": "1\n4\n1 2 4 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMinimizePermutationSubarrays"}}, "batch": {"id": "74323811-2d45-4d78-b1b5-a47d172bdf13", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Minimize_Permutation_Subarrays.cpp"}