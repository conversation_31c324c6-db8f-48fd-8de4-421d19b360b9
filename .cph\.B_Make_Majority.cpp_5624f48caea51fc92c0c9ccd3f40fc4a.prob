{"name": "<PERSON><PERSON> Make <PERSON>", "group": "Codeforces - Codeforces Round 958 (Div. 2)", "url": "https://codeforces.com/contest/1988/problem/B", "interactive": false, "memoryLimit": 512, "timeLimit": 1500, "tests": [{"input": "5\n1\n0\n1\n1\n2\n01\n9\n100000001\n9\n000011000\n", "output": "No\nYes\nNo\nYes\nNo\n", "id": 1721055767206}, {"id": 1721056164075, "input": "1\n13\n1111111101111", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMakeMajority"}}, "batch": {"id": "16f1355c-0ad1-4a6a-8f48-6af05bda0d9b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Make_Majority.cpp"}