{"name": "<PERSON><PERSON> Permutation", "group": "Codeforces - Codeforces Round 923 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1927/E", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1736431457893, "input": "5\n2 2\n3 2\n10 4\n13 4\n7 4\n", "output": "2 1\n1 3 2\n1 8 4 10 2 7 5 9 3 6\n4 10 1 13 5 9 2 12 6 8 3 11 7\n1 6 3 7 2 5 4\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "EKleverPermutation"}}, "batch": {"id": "45030f1d-11a5-428b-bda3-adde6a3be89a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\E_Klever_Permutation.cpp"}