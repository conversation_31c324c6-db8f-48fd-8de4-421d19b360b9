{"name": "C. Grouping Increases", "group": "Codeforces - Hello 2024", "url": "https://codeforces.com/problemset/problem/1919/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1736934069711, "input": "5\n5\n1 2 3 4 5\n8\n8 2 3 1 1 7 4 3\n5\n3 3 3 3 3\n1\n1\n2\n2 1\n", "output": "3\n1\n0\n0\n0\n"}, {"id": 1736948692700, "input": "1\n3\n1 2 2", "output": "0\r\n"}, {"id": 1736949559339, "input": "1\n5 \n1 2 1 1 2", "output": "0"}, {"id": 1736950348811, "input": "1\n5\n1 2 2 1 2 ", "output": "1\r\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CGroupingIncreases"}}, "batch": {"id": "a14aa93c-a5ef-43ff-be09-c24b23d0db7e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Grouping_Increases.cpp"}