{"name": "D. Ingen<PERSON>-2", "group": "Codeforces - Codeforces Round 946 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1974/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "10\n6\nNENSNE\n3\nWWW\n6\nNESSWS\n2\nSN\n2\nWE\n4\nSSNN\n4\nWESN\n2\nSS\n4\nEWNN\n4\nWEWE\n", "output": "RRHRRH\nNO\nHRRHRH\nNO\nNO\nRHRH\nRRHH\nRH\nRRRH\nRRHH\n", "id": 1734366983666}, {"id": 1734367916006, "input": "1\n4\nWESN", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DIngenuity2"}}, "batch": {"id": "664d77a4-d198-4cb0-8182-f81f585ce0b1", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Ingenuity_2.cpp"}