{"name": "C. Left and Right Houses", "group": "Codeforces - Codeforces Round 935 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1945/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720118071276, "input": "7\n3\n101\n6\n010111\n6\n011001\n3\n000\n3\n110\n3\n001\n4\n1100\n", "output": "2\n3\n2\n3\n0\n1\n0\n"}, {"id": 1720245583217, "input": "1\n3\n000", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CLeftAndRightHouses"}}, "batch": {"id": "654e6554-d78a-4cd2-ad91-93b91add02eb", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Left_and_Right_Houses.cpp"}