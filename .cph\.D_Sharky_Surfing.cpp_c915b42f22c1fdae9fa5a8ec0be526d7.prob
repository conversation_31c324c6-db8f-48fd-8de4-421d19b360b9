{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 988 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2037/D", "interactive": false, "memoryLimit": 256, "timeLimit": 3000, "tests": [{"id": 1733027061107, "input": "4\n2 5 50\n7 14\n30 40\n2 2\n3 1\n3 5\n18 2\n22 32\n4 3 50\n4 6\n15 18\n20 26\n34 38\n1 2\n8 2\n10 2\n1 4 17\n10 14\n1 6\n1 2\n1 2\n16 9\n1 2 10\n5 9\n2 3\n2 2\n", "output": "4\n-1\n1\n2\n"}, {"id": 1733027999357, "input": "1\n2 5 50\n7 14\n30 40\n\n2 2\n3 1\n3 5\n18 2\n22 32\n", "output": ""}, {"id": 1733031311128, "input": "1\n2 3 10\n3 3 \n5 8\n2 3\n2 3\n9 4", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DSharkySurfing"}}, "batch": {"id": "acd52ba7-0893-4bac-a691-10f43d0354e4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Sharky_Surfing.cpp"}