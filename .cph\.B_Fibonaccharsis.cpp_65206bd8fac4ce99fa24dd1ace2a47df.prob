{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 887 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1853/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1721144884562, "input": "8\n22 4\n3 9\n55 11\n42069 6\n69420 4\n69 1434\n1 3\n1 4\n", "output": "4\n0\n1\n1052\n11571\n0\n1\n0\n"}, {"id": 1721145391455, "input": "1\n196418 28", "output": "1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BFibonaccharsis"}}, "batch": {"id": "ff10cb8b-824e-4494-b65a-0f191e3a3f0f", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Fibonaccharsis.cpp"}