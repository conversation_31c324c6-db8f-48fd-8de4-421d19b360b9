{"name": "B<PERSON> and Sum", "group": "Codeforces - Codeforces Round 963 (Div. 2)", "url": "https://codeforces.com/contest/1993/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "7\n5\n1 3 5 7 9\n4\n4 4 4 4\n3\n2 3 4\n4\n3 2 2 8\n6\n4 3 6 1 2 1\n6\n3 6 1 2 1 2\n5\n999999996 999999997 999999998 999999999 1000000000\n", "output": "0\n0\n2\n4\n3\n3\n3\n", "id": 1722783373527}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BParityAndSum"}}, "batch": {"id": "d8664ff0-ad23-4d22-84b3-7cfcbc6755bc", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Parity_and_Sum.cpp"}