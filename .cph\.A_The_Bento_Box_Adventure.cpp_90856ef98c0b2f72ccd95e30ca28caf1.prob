{"name": "<PERSON>. The Bento Box Adventure", "group": "Codeforces - 2024 ICPC Asia Taichung Regional Contest (Unrated, Online Mirror, ICPC Rules, Preferably Teams)", "url": "https://codeforces.com/problemset/problem/2041/A", "interactive": false, "memoryLimit": 1024, "timeLimit": 1000, "tests": [{"input": "1 3 2 5\n", "output": "4\n", "id": 1733026015653}, {"input": "2 5 4 3\n", "output": "1\n", "id": 1733026015618}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ATheBentoBoxAdventure"}}, "batch": {"id": "e2db371a-7bbc-42b9-894e-eaeb9cdf159a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_The_Bento_Box_Adventure.cpp"}