{"name": "B. Forming Triangles", "group": "Codeforces - Educational Codeforces Round 161 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1922/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720455884237, "input": "4\n7\n1 1 1 1 1 1 1\n4\n3 2 1 3\n3\n1 2 3\n1\n1\n", "output": "35\n2\n0\n0\n"}, {"id": 1720462926597, "input": "1\n15\n1 2 3 3 3 4 4 4 5 5 5 5 5 6 6 ", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BFormingTriangles"}}, "batch": {"id": "5719c4f0-3006-4b99-8c7a-fde75be94f49", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Forming_Triangles.cpp"}