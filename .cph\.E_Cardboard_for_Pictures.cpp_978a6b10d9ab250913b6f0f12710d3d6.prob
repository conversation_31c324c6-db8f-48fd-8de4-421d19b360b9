{"name": "<PERSON>. <PERSON> for Pictures", "group": "Codeforces - Codeforces Round 886 (Div. 4)", "url": "https://codeforces.com/problemset/problem/1850/E", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719663364387, "input": "10\n3 50\n3 2 1\n1 100\n6\n5 500\n2 2 2 2 2\n2 365\n3 4\n2 469077255466389\n10000 2023\n10 635472106413848880\n9181 4243 7777 1859 2017 4397 14 9390 2245 7225\n7 176345687772781240\n9202 9407 9229 6257 7743 5738 7966\n14 865563946464579627\n3654 5483 1657 7571 1639 9815 122 9468 3079 2666 5498 4540 7861 5384\n19 977162053008871403\n9169 9520 9209 9013 9300 9843 9933 9454 9960 9167 9964 9701 9251 9404 9462 9277 9661 9164 9161\n18 886531871815571953\n2609 10 5098 9591 949 8485 6385 4586 1064 5412 6564 8460 2245 6552 5089 8353 3803 3764\n", "output": "1\n2\n4\n5\n7654321\n126040443\n79356352\n124321725\n113385729\n110961227\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ECardboardForPictures"}}, "batch": {"id": "4adc2eec-1d88-4afc-b5e1-f4ca62ee9ec0", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\E_Cardboard_for_Pictures.cpp"}