{"name": "The Legend of <PERSON><PERSON> the Frog", "group": "Codeforces", "url": "https://m2.codeforces.com/contest/2009/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1725375108953, "input": "3\n9 11 3\n0 10 8\n1000000 100000 10\n", "output": "8\n4\n199999\n"}, {"id": 1725379083866, "input": "1\n5 6 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "TheLegendOfFreyaTheFrog"}}, "batch": {"id": "5dfcd22f-9086-443a-aa8e-a5bc5d89239a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\The_Legend_of_Freya_the_Frog.cpp"}