{"name": "C. Non-coprime Split", "group": "Codeforces - Codeforces Round 895 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1872/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1718287792927, "input": "11\n11 15\n1 3\n18 19\n41 43\n777 777\n8000000 10000000\n2000 2023\n1791791 1791791\n1 4\n2 3\n9840769 9840769\n", "output": "6 9\n-1\n14 4\n36 6\n111 666\n4000000 5000000\n2009 7\n-1\n2 2\n-1\n6274 9834495\n"}, {"id": 1718289285523, "input": "1\n2 5", "output": "-1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CNonCoprimeSplit"}}, "batch": {"id": "634a6d48-64fb-48bb-abfe-c356ab8a16ab", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Non_coprime_Split.cpp"}