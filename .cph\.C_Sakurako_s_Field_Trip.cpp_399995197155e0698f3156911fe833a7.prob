{"name": "<PERSON><PERSON>'s Field Trip", "group": "Codeforces - Codeforces Round 981 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2033/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1733841685709, "input": "9\n5\n1 1 1 2 3\n6\n2 1 2 2 1 1\n4\n1 2 1 1\n6\n2 1 1 2 2 4\n4\n2 1 2 3\n6\n1 2 2 1 2 1\n5\n4 5 5 1 5\n7\n1 4 3 5 1 1 3\n7\n3 1 3 2 2 3 3\n", "output": "1\n2\n1\n0\n0\n1\n1\n0\n2\n"}, {"id": 1733842367043, "input": "1\n6\n2 1 2 2 1 1", "output": "2"}, {"id": 1733842630737, "input": "1\n2\n1 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CSakurakosFieldTrip"}}, "batch": {"id": "6d4d94bc-73bf-4659-b459-44248a92c406", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Sakurako_s_Field_Trip.cpp"}