{"name": "C. The Trail", "group": "Codeforces - Codeforces Round 996 (Div. 2)", "url": "https://codeforces.com/contest/2055/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1736781564843, "input": "4\n3 3\nDRRD\n0 2 3\n0 0 0\n3 1 0\n4 5\nDRRRRDD\n0 1 0 2 3\n0 0 0 0 0\n-1 0 -3 -3 0\n0 0 0 -1 0\n2 3\nRRD\n0 0 0\n0 1 0\n5 5\nDDDDRRRR\n0 25 2 9 11\n0 6 13 20 22\n0 17 24 1 8\n0 3 10 12 19\n0 0 0 0 0\n", "output": "1 2 3\n2 3 1\n3 1 2\n-6 1 0 2 3\n7 -1 3 2 -11\n-1 0 -3 -3 7\n0 0 0 -1 1\n0 -1 1\n0 1 -1\n18 25 2 9 11\n4 6 13 20 22\n15 17 24 1 8\n21 3 10 12 19\n7 14 16 23 5\n"}, {"id": 1736782449353, "input": "1\n3 3\nDRRD\n0 2 3\n0 0 0\n3 1 0", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTheTrail"}}, "batch": {"id": "61d18972-e430-4927-8845-c6b6b826c83c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C_The_Trail.cpp"}