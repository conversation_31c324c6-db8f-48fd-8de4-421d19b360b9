{"name": "<PERSON><PERSON>", "group": "Codeforces - Educational Codeforces Round 170 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/2025/C", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1730265952642, "input": "4\n10 2\n5 2 4 3 4 3 4 5 3 2\n5 1\n10 11 10 11 10\n9 3\n4 5 4 4 6 5 4 4 6\n3 2\n1 3 1\n", "output": "6\n3\n9\n2\n"}, {"id": 1730267402323, "input": "1\n10 2\n5 2 4 3 4 3 4 5 3 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CNewGame"}}, "batch": {"id": "796bb1d7-06b4-4e55-8ca8-d6381f16057a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_New_Game.cpp"}