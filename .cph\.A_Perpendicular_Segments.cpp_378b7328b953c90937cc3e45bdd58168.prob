{"name": "A. Perpendicular Segments", "group": "Codeforces - Educational Codeforces Round 171 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2026/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730126432313, "input": "4\n1 1 1\n3 4 1\n4 3 3\n3 4 4\n", "output": "0 0 1 0\n0 0 0 1\n2 4 2 2\n0 1 1 1\n0 0 1 3\n1 2 4 1\n0 1 3 4\n0 3 3 0\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "APerpendicularSegments"}}, "batch": {"id": "9ab13953-d5c0-4653-bc2f-67de1a16f083", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Perpendicular_Segments.cpp"}