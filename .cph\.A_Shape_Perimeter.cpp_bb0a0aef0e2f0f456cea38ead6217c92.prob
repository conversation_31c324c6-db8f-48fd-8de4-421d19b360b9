{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 997 (Div. 2)", "url": "https://codeforces.com/contest/2056/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n4 3\n1 1\n2 2\n2 1\n1 2\n1 2\n1 1\n6 7\n3 6\n1 1\n3 1\n6 6\n5 4\n6 1\n", "output": "32\n8\n96\n", "id": 1737124886368}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AShapePerimeter"}}, "batch": {"id": "e3d5f166-a79d-4e77-8cb5-bdb70940a665", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Shape_Perimeter.cpp"}