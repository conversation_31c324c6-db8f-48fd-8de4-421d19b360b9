{"name": "B. Battle Cows", "group": "Codeforces - Codeforces Global Round 25", "url": "https://codeforces.com/problemset/problem/1951/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n6 1\n12 10 14 11 8 3\n6 5\n7 2 727 10 12 13\n2 2\n1000000000 1\n", "output": "1\n2\n0\n", "id": 1720115542668}, {"id": 1720117033730, "input": "1\n6 1\n439338568 624973743 287900010 481188218 118617625 340016478", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BBattleCows"}}, "batch": {"id": "615de5e1-ac59-4635-b68f-cbe20da9e8d8", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Battle_Cows.cpp"}