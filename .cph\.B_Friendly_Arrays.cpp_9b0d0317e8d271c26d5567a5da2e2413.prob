{"name": "B. Friendly Arrays", "group": "Codeforces - CodeTON Round 6 (Div. 1 + Div. 2, Rated, Prizes!)", "url": "https://codeforces.com/problemset/problem/1870/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720936912379, "input": "2\n2 3\n0 1\n1 2 3\n3 1\n1 1 2\n1\n", "output": "0 1\n2 3\n"}, {"id": 1720937502408, "input": "1\n1 3\n0\n0 1 2", "output": "0 3"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BFriendlyArrays"}}, "batch": {"id": "6a463df6-e1a4-4f3d-9090-eeb28c187b6a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Friendly_Arrays.cpp"}