{"name": "<PERSON><PERSON> and <PERSON>ing", "group": "Codeforces - Codeforces Round 971 (Div. 4)", "url": "https://codeforces.com/problemset/problem/2009/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "3\n5\n1 0\n1 1\n3 0\n5 0\n2 1\n3\n0 0\n1 0\n3 0\n9\n1 0\n2 0\n3 0\n4 0\n5 0\n2 1\n7 1\n8 1\n9 1\n", "output": "4\n0\n8\n", "id": 1734089967142}, {"id": 1734091409873, "input": "1\n5\n1 0\n1 1\n3 0\n5 0\n2 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DSatyamAndCounting"}}, "batch": {"id": "1262cb25-b1c7-48d9-91eb-f3dfd9c9f7f6", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Satyam_and_Counting.cpp"}