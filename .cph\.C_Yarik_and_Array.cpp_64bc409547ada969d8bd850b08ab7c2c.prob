{"name": "<PERSON><PERSON> and <PERSON><PERSON>", "group": "Codeforces - Codeforces Round 909 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1899/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1717938067968, "input": "7\n5\n1 2 3 4 5\n4\n9 9 8 8\n6\n-1 4 -1 0 5 -4\n4\n-1 2 4 -3\n1\n-1000\n3\n101 -99 101\n20\n-10 5 -8 10 6 -10 7 9 -2 -6 7 2 -4 6 -1 7 -6 -7 4 1\n", "output": "15\n17\n8\n4\n-1000\n101\n10\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CYarikAndArray"}}, "batch": {"id": "c0cc0fe7-8d24-4e21-8610-e9e1599011e6", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Yarik_and_Array.cpp"}