{"name": "C. The zombies are NOT coming!!!", "group": "Codeforces - NJACK CP Monthly Contest (August)", "url": "https://codeforces.com/gym/544601/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "2\n3 2\n1 3 5\n4 1\n5 2 3 5\n", "output": "2\n2\n", "id": 1724428740335}, {"id": 1724429184745, "input": "1\n2 3\n6 6", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTheZombiesAreNOTComing"}}, "batch": {"id": "180984e4-461b-49cb-a424-09019e03b87f", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_The_zombies_are_NOT_coming.cpp"}