{"name": "D. Super-Permutation", "group": "Codeforces - Codeforces Round 867 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1822/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1723462283559, "input": "4\n1\n2\n3\n6\n", "output": "1\n2 1\n-1\n6 5 2 3 4 1\n"}, {"id": 1723469089984, "input": "1\n8", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DSuperPermutation"}}, "batch": {"id": "ad2377e8-fb43-4b4b-8047-248713fa148b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Super_Permutation.cpp"}