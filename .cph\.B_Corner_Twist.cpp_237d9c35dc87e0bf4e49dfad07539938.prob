{"name": "<PERSON><PERSON> Twist", "group": "Codeforces - Codeforces Round #956 (Div. 2) and ByteRace 2024", "url": "https://codeforces.com/contest/1983/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1720365476289, "input": "7\n3 3\n000\n000\n000\n111\n111\n111\n4 4\n0000\n0000\n0000\n0000\n2100\n1200\n0012\n0021\n4 4\n1020\n1200\n1210\n0000\n0000\n1200\n2200\n0000\n3 3\n012\n012\n012\n010\n111\n011\n8 8\n00000000\n00000000\n00000000\n00000000\n00000000\n00000000\n00000000\n10000000\n00000000\n01200000\n02010000\n00102000\n00020100\n00001020\n00000210\n10000000\n2 7\n0000000\n0000000\n2220111\n0111222\n2 7\n0000000\n0100010\n2220111\n1210202\n", "output": "YES\nYES\nYES\nNO\nYES\nNO\nYES\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "batch": {"id": "8f83486c-b0cf-4a4b-9128-abffb4cf95a3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Corner_Twist.cpp"}