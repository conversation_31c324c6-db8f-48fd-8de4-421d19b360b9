{"name": "A. <PERSON> Point", "group": "Codeforces - Educational Codeforces Round 169 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2004/problem/0", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1723732666343, "input": "3\n2\n3 8\n2\n5 6\n6\n1 2 3 4 5 10\n", "output": "YES\nNO\nNO\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AClosestPoint"}}, "batch": {"id": "d6202903-7858-477e-bd17-4b60bc089f3d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Closest_Point.cpp"}