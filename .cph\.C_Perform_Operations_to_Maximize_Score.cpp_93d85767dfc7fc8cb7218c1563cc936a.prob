{"name": "C. Perform Operations to Maximize Score", "group": "Codeforces - Codeforces Round 965 (Div. 2)", "url": "https://codeforces.com/contest/1998/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 3000, "tests": [{"id": 1723304315475, "input": "8\n2 10\n3 3\n1 1\n3 10\n3 3 3\n0 0 0\n4 4\n2 1 5 1\n0 1 0 1\n5 4\n7 5 2 5 4\n0 0 1 0 1\n5 1\n5 15 15 2 11\n1 0 0 1 1\n5 2\n10 11 4 10 15\n1 1 0 1 0\n4 4\n1 1 2 5\n1 1 0 0\n2 1000000000\n1000000000 1000000000\n1 1\n", "output": "16\n6\n8\n13\n21\n26\n8\n3000000000\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CPerformOperationsToMaximizeScore"}}, "batch": {"id": "29fbf281-b9b5-4ccc-85a8-e555cafb1dc2", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Perform_Operations_to_Maximize_Score.cpp"}