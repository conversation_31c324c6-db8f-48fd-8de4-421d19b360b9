{"name": "C. Increasing Sequence with Fixed OR", "group": "Codeforces - Codeforces Round 958 (Div. 2)", "url": "https://codeforces.com/contest/1988/problem/C", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1721058556850, "input": "4\n1\n3\n14\n23\n", "output": "1\n1\n3\n1 2 3\n4\n4 10 12 14\n5\n7 18 21 22 23\n"}, {"id": 1721061170224, "input": "1\n9", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CIncreasingSequenceWithFixedOR"}}, "batch": {"id": "6986e29c-c190-442b-aa4e-916ede8963d0", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Increasing_Sequence_with_Fixed_OR.cpp"}