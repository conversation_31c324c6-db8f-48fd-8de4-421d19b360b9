{"name": "B. Buying Lemonade", "group": "Codeforces - Codeforces Round 980 (Div. 2)", "url": "https://codeforces.com/contest/2024/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n2 1\n1 1\n2 2\n1 2\n3 4\n2 1 3\n10 50\n1 1 3 8 8 9 12 13 27 27\n2 1000000000\n1000000000 500000000\n", "output": "1\n2\n5\n53\n1000000000\n", "id": 1729415682890}, {"id": 1729417135544, "input": "1\n10 50\n1 1 3 8 8 9 12 13 27 27", "output": ""}, {"id": 1729418342343, "input": "1\n6 30\n5 5 7 3 2 8", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BBuyingLemonade"}}, "batch": {"id": "7c0c0a6b-2894-4fbf-92e3-f50013c6c071", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Buying_Lemonade.cpp"}