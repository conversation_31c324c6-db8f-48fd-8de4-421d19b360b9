{"name": "<PERSON><PERSON> and an Infinite Sequence", "group": "Codeforces - Codeforces Round 949 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1981/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1728901013345, "input": "9\n0 0\n0 1\n0 2\n1 0\n5 2\n10 1\n20 3\n1145 14\n19198 10\n", "output": "0\n1\n3\n1\n7\n11\n23\n1279\n19455\n"}, {"id": 1728901940796, "input": "1\n0 1", "output": ""}, {"id": 1728910953440, "input": "1\n0 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BTurtleAndAnInfiniteSequence"}}, "batch": {"id": "673b952a-ebd8-4f6f-89ad-79e540ed3ba6", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Turtle_and_an_Infinite_Sequence.cpp"}