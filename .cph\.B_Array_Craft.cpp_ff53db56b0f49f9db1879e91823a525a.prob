{"name": "B. <PERSON>y Craft", "group": "Codeforces - Codeforces Round 960 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1990/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n2 2 1\n4 4 3\n6 5 1\n", "output": "1 1\n1 -1 1 1\n1 1 -1 1 1 -1\n", "id": 1728344933648}, {"id": 1728345284389, "input": "1\n5 2 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BArrayCraft"}}, "batch": {"id": "08fd231d-9871-45b2-bba6-f9d986150cb6", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Array_Craft.cpp"}