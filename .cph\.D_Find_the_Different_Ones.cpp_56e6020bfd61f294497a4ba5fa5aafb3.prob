{"name": "<PERSON><PERSON> Find the Different Ones!", "group": "Codeforces - Codeforces Round 923 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1927/D", "interactive": false, "memoryLimit": 256, "timeLimit": 5000, "tests": [{"id": 1730442420992, "input": "5\n5\n1 1 2 1 1\n3\n1 5\n1 2\n1 3\n6\n30 20 20 10 10 20\n5\n1 2\n2 3\n2 4\n2 6\n3 5\n4\n5 2 3 4\n4\n1 2\n1 4\n2 3\n2 4\n5\n1 4 3 2 4\n5\n1 5\n2 4\n3 4\n3 5\n4 5\n5\n2 3 1 4 2\n7\n1 2\n1 4\n1 5\n2 4\n2 5\n3 5\n4 5\n", "output": "2 3\n-1 -1\n1 3\n\n2 1\n-1 -1\n4 2\n4 6\n5 3\n\n1 2\n1 2\n2 3\n3 2\n\n1 3\n2 4\n3 4\n5 3\n5 4\n\n1 2\n4 2\n1 3\n2 3\n3 2\n5 4\n5 4\n"}, {"id": 1730442721259, "input": "1\n4\n1 1 1 1\n6\n1 2\n1 3\n1 4\n2 3\n2 4\n3 4", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DFindTheDifferentOnes"}}, "batch": {"id": "5e6372d5-4fcd-4b0d-a2de-009a05cc5843", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Find_the_Different_Ones.cpp"}