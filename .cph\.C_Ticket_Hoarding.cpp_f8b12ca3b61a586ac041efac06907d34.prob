{"name": "<PERSON><PERSON> Hoarding", "group": "Codeforces - Codeforces Global Round 25", "url": "https://codeforces.com/problemset/problem/1951/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1736181470845, "input": "4\n4 2 3\n8 6 4 2\n4 2 8\n8 6 4 2\n5 100 1\n10000 1 100 10 1000\n6 3 9\n5 5 5 5 5 5\n", "output": "10\n64\n1\n72\n"}, {"id": 1736183410353, "input": "1\n3 8 20\n939006130 939006130 331197969", "output": "13917657440"}, {"id": 1736914396657, "input": "1\n4 2 3\n8 6 4 2", "output": "10"}, {"id": 1736917245919, "input": "1\n4 3 10\n1 2 3 4", "output": "58"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTicketHoarding"}}, "batch": {"id": "020160fd-f1bc-4f7c-9eb6-eaf80765fd52", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Ticket_Hoarding.cpp"}