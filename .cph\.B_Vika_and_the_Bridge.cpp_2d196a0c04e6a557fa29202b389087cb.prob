{"name": "B. <PERSON> and the Bridge", "group": "Codeforces - Codeforces Round 885 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1848/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1721901956849, "input": "5\n5 2\n1 1 2 1 1\n7 3\n1 2 3 3 3 2 1\n6 6\n1 2 3 4 5 6\n8 4\n1 2 3 4 2 3 1 4\n3 1\n1 1 1\n", "output": "0\n1\n2\n2\n0\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BVikaAndTheBridge"}}, "batch": {"id": "bfd1afaa-eb0c-493a-94c8-c550b6bf9f06", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Vika_and_the_Bridge.cpp"}