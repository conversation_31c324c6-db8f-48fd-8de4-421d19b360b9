{"name": "<PERSON><PERSON> Subsequences", "group": "Codeforces - Codeforces Round 997 (Div. 2)", "url": "https://codeforces.com/contest/2056/problem/C", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"input": "3\n6\n9\n15\n", "output": "1 1 2 3 1 2\n7 3 3 7 5 3 7 7 3\n15 8 8 8 15 5 8 1 15 5 8 15 15 15 8\n", "id": 1737126078624}, {"id": 1737126527278, "input": "10\n8\n9\n10\n11\n12\n15\n16\n17\n20\n50\n", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CPalindromicSubsequences"}}, "batch": {"id": "ea6dbd85-fd4d-4e69-bc58-def13b1e3540", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Palindromic_Subsequences.cpp"}