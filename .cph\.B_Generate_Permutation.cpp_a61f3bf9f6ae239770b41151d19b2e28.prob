{"name": "B. Generate Permutation", "group": "Codeforces - Codeforces Round 967 (Div. 2)", "url": "https://codeforces.com/contest/2001/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1500, "tests": [{"input": "2\n1\n2\n", "output": "1\n-1\n", "id": 1724165662907}, {"id": 1724166230803, "input": "1\n5\n", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BGeneratePermutation"}}, "batch": {"id": "3bfba839-87a2-4c5b-b1ef-8bb472d3d6f9", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Generate_Permutation.cpp"}