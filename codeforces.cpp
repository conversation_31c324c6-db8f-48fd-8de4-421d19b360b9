#include <bits/stdc++.h>
using namespace std;

#define aa "Ans is "<<
#define ll long long
#define pb push_back
#define endl "\n"
#define bug1 cout<<"bug "<<endl;
#define bug2 cout<<"bug bug"<<endl;
#define bug3 cout<<"bug bug bug"<<endl;

// Always solve with proper logic, don't Rush.

int main() {
    string x;
    cin>>x;
    vector <char> ans;
    int len = x.size();
    for(int i=len-1; i>=0; i--) ans.push_back(x[i]);

    if(ans[len-1] == '-') {
        cout<<"-";
        len--;
    }
    bool flag = true;
    for(int i=0; i<len; i++){
        if(ans[i] == '0' && flag == true) continue;
        flag = false;
        cout<<ans[i];
    }
    return 0;
}