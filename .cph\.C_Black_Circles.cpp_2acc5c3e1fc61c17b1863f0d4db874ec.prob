{"name": "C. Black Circles", "group": "Codeforces - EPIC Institute of Technology Round August 2024 (Div. 1 + Div. 2)", "url": "https://codeforces.com/contest/2002/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "7\n3\n2 5\n2 14\n10 13\n4 9 9 7\n3\n10 11\n6 9\n12 12\n14 13 4 8\n1\n5 7\n12 6 11 13\n2\n1000000000 2\n2 1000000000\n1 1 2 2\n1\n999999998 1000000000\n999999999 999999999 1 1\n1\n1000000000 1\n1 1000000000 1 1\n10\n989237121 2397081\n206669655 527238537\n522705783 380636165\n532545346 320061691\n207818728 199485303\n884520552 315781807\n992311437 802563521\n205138355 324818663\n223575704 395073023\n281560523 236279118\n216941610 572010615 323956540 794523071\n", "output": "YES\nNO\nYES\nYES\nYES\nNO\nYES\n", "id": 1723389790357}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CBlackCircles"}}, "batch": {"id": "6a8eff21-d4f1-4970-9cc3-f56f7b1ab87a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Black_Circles.cpp"}