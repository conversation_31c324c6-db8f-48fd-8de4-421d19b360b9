{"name": "<PERSON><PERSON> and a Sum of Sum of Digits", "group": "Codeforces - Codeforces Round 928 (Div. 4)", "url": "https://codeforces.com/problemset/problem/1926/C", "interactive": false, "memoryLimit": 256, "timeLimit": 500, "tests": [{"id": 1720431378553, "input": "7\n12\n1\n2\n3\n1434\n2024\n200000\n", "output": "51\n1\n3\n6\n18465\n28170\n4600002\n"}, {"id": 1720458124640, "input": "1\n7320", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CVladAndASumOfSumOfDigits"}}, "batch": {"id": "3a0f62bf-4c83-450f-aef0-b38078495645", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Vlad_and_a_Sum_of_Sum_of_Digits.cpp"}