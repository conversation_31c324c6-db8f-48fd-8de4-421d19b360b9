{"name": "E. Secret Box", "group": "Codeforces - Codeforces Round 952 (Div. 4)", "url": "https://codeforces.com/problemset/problem/1985/E", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1720019469423, "input": "7\n3 3 3 8\n3 3 3 18\n5 1 1 1\n2 2 2 7\n3 4 2 12\n4 3 1 6\n1800 1800 1800 4913000000\n", "output": "8\n2\n5\n0\n4\n4\n1030301\n"}, {"id": 1720019593309, "input": "1\n6 21 32 2520", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ESecretBox"}}, "batch": {"id": "4c7a359e-5d80-4b54-a945-c16268b9dfc8", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\E_Secret_Box.cpp"}