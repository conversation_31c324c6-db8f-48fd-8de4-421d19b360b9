{"name": "<PERSON><PERSON> on Bets", "group": "Codeforces - Codeforces Round 951 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1979/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "6\n3\n3 2 7\n2\n3 3\n5\n5 5 5 5 5\n6\n7 9 3 17 9 13\n3\n6 3 2\n5\n9 4 6 8 3\n", "output": "27 41 12\n1 1\n-1\n1989 1547 4641 819 1547 1071\n-1\n8 18 12 9 24\n", "id": 1720070133457}, {"id": 1720075730296, "input": "1\n15\n19 15 19 11 19 20 11 19 19 11 18 14 11 13 19", "output": ""}, {"id": 1720076883605, "input": "2\n11\n11 11 11 11 11 11 11 11 11 11 11\n7\n7 7 7 7 7 7 7", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CEarningOnBets"}}, "batch": {"id": "597b0109-29a3-45fa-9b81-73eb85cfe311", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Earning_on_Bets.cpp"}