{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 962 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1996/C", "interactive": false, "memoryLimit": 256, "timeLimit": 5000, "tests": [{"id": 1722139086142, "input": "3\n5 3\nabcde\nedcba\n1 5\n1 4\n3 3\n4 2\nzzde\nazbe\n1 3\n1 4\n6 3\nuwuwuw\nwuwuwu\n2 4\n1 3\n1 6\n", "output": "0\n1\n0\n2\n2\n1\n1\n0\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CSort"}}, "batch": {"id": "d737b1c3-7ed6-45c9-9b52-8e513219bdf9", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Sort.cpp"}