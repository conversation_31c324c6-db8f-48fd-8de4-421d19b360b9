{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 962 (Div. 3)", "url": "https://codeforces.com/contest/1996/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "3\n2\n6\n8\n", "output": "1\n2\n2\n", "id": 1722004622235}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ALegs"}}, "batch": {"id": "ca96ac81-6186-4375-860e-29d7129fa13d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Legs.cpp"}