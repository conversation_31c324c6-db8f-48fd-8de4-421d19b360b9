{"files.associations": {"iostream": "cpp", "ostream": "cpp", "new": "cpp", "cstdlib": "cpp", "cmath": "cpp", "complex": "cpp", "bitset": "cpp", "vector": "cpp", "memory": "cpp", "system_error": "cpp", "thread": "cpp", "typeindex": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "cctype": "cpp", "cfenv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstring": "cpp", "ctime": "cpp", "cuchar": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory_resource": "cpp", "numeric": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "set": "cpp", "string": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "scoped_allocator": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp", "valarray": "cpp", "climits": "cpp"}, "C_Cpp.errorSquiggles": "disabled"}