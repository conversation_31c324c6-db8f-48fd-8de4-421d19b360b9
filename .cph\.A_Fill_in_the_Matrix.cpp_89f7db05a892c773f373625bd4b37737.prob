{"name": "<PERSON><PERSON> in the Matrix", "group": "Codeforces - Codeforces Round 896 (Div. 1)", "url": "https://codeforces.com/problemset/problem/1868/A", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1733241859666, "input": "4\n4 3\n1 16\n6 6\n2 1\n", "output": "3\n1 0 2\n0 2 1\n1 0 2\n0 2 1\n2\n14 7 15 4 10 0 8 6 1 2 3 5 9 11 12 13\n6\n3 0 1 4 2 5\n5 2 1 0 4 3\n1 3 2 4 5 0\n4 1 3 2 5 0\n4 2 5 3 0 1\n2 4 0 5 1 3\n0\n0\n0\n"}, {"id": 1733242521196, "input": "2\n6 2\n7 2", "output": ""}, {"id": 1733242529515, "input": "1\n6 2", "output": ""}, {"id": 1733242586443, "input": "1\n7 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AFillInTheMatrix"}}, "batch": {"id": "24fab8e1-af38-4aea-9851-b7877eb664ae", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Fill_in_the_Matrix.cpp"}