{"name": "<PERSON><PERSON> to the Olympiad", "group": "Codeforces - Hello 2025", "url": "https://codeforces.com/contest/2057/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1736002763891, "input": "8\n0 2\n0 8\n1 3\n6 22\n128 137\n69 98\n115 127\n0 1073741823\n", "output": "1 2 0\n8 7 1\n2 1 3\n7 16 11\n134 132 137\n98 85 76\n123 121 118\n965321865 375544086 12551794\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTripToTheOlympiad"}}, "batch": {"id": "f6221cc1-2fb1-4f3e-9ecc-baf9180a44b7", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Trip_to_the_Olympiad.cpp"}