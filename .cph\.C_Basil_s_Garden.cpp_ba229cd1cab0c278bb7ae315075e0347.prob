{"name": "<PERSON><PERSON>'s Garden", "group": "Codeforces - EPIC Institute of Technology Round Summer 2024 (Div. 1 + Div. 2)", "url": "https://codeforces.com/contest/1987/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719764712898, "input": "4\n3\n1 1 2\n2\n3 1\n1\n9\n5\n7 4 4 3 2\n", "output": "4\n3\n9\n7\n"}, {"id": 1719765918154, "input": "5\n5\n1 1 1 1 1\n6\n9 9 9 9 9 9\n6\n9 6 4 3 2 1\n8\n10 1 1 1 1 1 1 1 \n6\n4 3 2 5 8 1\n", "output": "5\n14\n7\n10\n12\n"}, {"id": 1719766019339, "input": "1\n6\n9 6 4 3 2 1", "output": "9"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CBasilsGarden"}}, "batch": {"id": "3ef48309-97bf-4197-861b-a36db311b9a4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Basil_s_Garden.cpp"}