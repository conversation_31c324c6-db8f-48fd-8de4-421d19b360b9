{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 941 (Div. 1)", "url": "https://codeforces.com/problemset/problem/1965/A", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1736438394115, "input": "7\n5\n3 3 3 3 3\n2\n1 7\n7\n1 3 9 7 4 2 100\n3\n1 2 3\n6\n2 1 3 4 2 4\n8\n5 7 2 9 6 3 3 2\n1\n1000000000\n", "output": "<PERSON>\nBob\nAlice\nAlice\n<PERSON>\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AEverythingNim"}}, "batch": {"id": "390f36b7-c6ea-4955-acc6-fb0ed512facb", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Everything_Nim.cpp"}