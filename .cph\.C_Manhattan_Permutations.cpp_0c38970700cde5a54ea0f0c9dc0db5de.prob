{"name": "C. Manhattan Permutations", "group": "Codeforces - Codeforces Round 953 (Div. 2)", "url": "https://codeforces.com/contest/1978/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1718535157512, "input": "8\n3 4\n4 5\n7 0\n1 1000000000000\n8 14\n112 777\n5 12\n5 2\n", "output": "Yes\n3 1 2\nNo\nYes\n1 2 3 4 5 6 7\nNo\nYes\n8 2 3 4 5 6 1 7\nNo\nYes\n5 4 3 1 2\nYes\n2 1 3 4 5\n"}, {"id": 1718535261866, "input": "1\n8 14", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CManhattanPermutations"}}, "batch": {"id": "e61d9b1e-f0b9-464c-90e7-9410541c944d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Manhattan_Permutations.cpp"}