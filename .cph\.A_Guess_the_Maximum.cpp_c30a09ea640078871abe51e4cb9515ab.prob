{"name": "<PERSON><PERSON> Guess the Maximum", "group": "Codeforces - Codeforces Round 951 (Div. 2)", "url": "https://codeforces.com/contest/1979/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1717684527979, "input": "6\n4\n2 4 1 7\n5\n1 2 3 4 5\n2\n1 1\n3\n37 8 16\n5\n10 10 10 10 9\n10\n3 12 9 5 2 3 2 9 8 2\n", "output": "3\n1\n0\n15\n9\n2\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AGuessTheMaximum"}}, "batch": {"id": "9469bb6c-3aa6-4a31-954f-70c20d66190e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Guess_the_Maximum.cpp"}