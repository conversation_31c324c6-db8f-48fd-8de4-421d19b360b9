{"name": "<PERSON><PERSON> and Mrs <PERSON>", "group": "Codeforces - Codeforces Round 974 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2014/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1733979478487, "input": "6\n2 1 1\n1 2\n4 1 2\n1 2\n2 4\n7 2 3\n1 2\n1 3\n6 7\n5 1 2\n1 2\n3 5\n9 2 1\n2 8\n9 2 4\n7 9\n4 8\n1 3\n2 3\n", "output": "1 1\n2 1\n1 4\n1 1\n1 1\n3 4\n"}, {"id": 1733980687619, "input": "1\n7 3 4\n3 5\n1 2\n4 5\n3 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DRobertHoodAndMrsHood"}}, "batch": {"id": "b3233276-5646-4884-b0c9-b9a050cf6700", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\<PERSON>_<PERSON>_and_<PERSON>_<PERSON>.cpp"}