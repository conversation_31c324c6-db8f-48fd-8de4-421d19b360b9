{"name": "B2. The Strict Teacher (Hard Version)", "group": "Codeforces - Codeforces Round 972 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2005/B2", "interactive": false, "memoryLimit": 256, "timeLimit": 1500, "tests": [{"input": "2\n8 1 1\n6\n3\n10 3 3\n1 4 8\n2 3 10\n", "output": "5\n1\n1\n2\n", "id": 1728392500524}, {"id": 1728393853117, "input": "1\n4 2 2 \n3 1\n2 4", "output": "1\n1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "B2TheStrictTeacherHardVersion"}}, "batch": {"id": "c19ed418-1dd7-4a83-aa75-f4eb051a026e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_2_The_Strict_Teacher_Hard_Version.cpp"}