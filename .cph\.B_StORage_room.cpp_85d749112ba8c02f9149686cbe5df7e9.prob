{"name": "B. StORage room", "group": "Codeforces - Codeforces Round 912 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1903/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720544258617, "input": "4\n1\n0\n4\n0 3 3 5\n3 0 3 7\n3 3 0 7\n5 7 7 0\n5\n0 7 7 5 5\n7 0 3 2 6\n7 3 0 3 7\n5 2 3 0 4\n5 6 7 4 0\n3\n0 0 1\n0 0 0\n1 0 0\n", "output": "YES\n7\nYES\n1 3 2 5\nYES\n5 2 3 0 4\nNO\n"}, {"id": 1720546258680, "input": "1\n5\n0 1 1 1 1\n1 0 1 0 0\n1 1 0 0 0\n1 0 0 0 0\n1 0 0 0 0", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BStORageRoom"}}, "batch": {"id": "5821f60e-7ded-43fa-9cbc-d8c1f4606247", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_StORage_room.cpp"}