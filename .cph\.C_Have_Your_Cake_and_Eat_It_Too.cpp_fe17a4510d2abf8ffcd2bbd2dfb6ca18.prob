{"name": "<PERSON><PERSON> Have Your Cake and Eat It Too", "group": "Codeforces - Codeforces Round 956 (Div. 2) and ByteRace 2024", "url": "https://codeforces.com/problemset/problem/1983/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1734327858078, "input": "10\n5\n5 1 1 1 1\n1 1 5 1 1\n1 1 1 1 5\n6\n1 2 3 4 5 6\n5 6 1 2 3 4\n3 4 5 6 1 2\n4\n4 4 4 4\n4 4 4 4\n4 4 4 4\n5\n5 10 5 2 10\n9 6 9 7 1\n10 7 10 2 3\n3\n4 5 2\n6 1 4\n1 8 2\n3\n10 4 10\n8 7 9\n10 4 10\n7\n57113 65383 19795 53580 74452 3879 23255\n12917 16782 89147 93107 27365 15044 43095\n33518 63581 33565 34112 46774 44151 41756\n6\n6 3 1 8 7 1\n10 2 6 2 2 4\n10 9 2 1 2 2\n5\n5 5 4 5 5\n1 6 3 8 6\n2 4 1 9 8\n10\n1 1 1 1 1001 1 1 1001 1 1\n1 1 1 1 1 1 2001 1 1 1\n1 1 1 1 1 1001 1 1 1 1001\n", "output": "1 1 2 3 4 5\n5 6 1 2 3 4\n-1\n-1\n1 1 3 3 2 2\n-1\n1 2 3 4 5 7\n3 6 1 1 2 2\n1 2 3 4 5 5\n1 5 6 7 8 10\n"}, {"id": 1734329170930, "input": "1\n11\n21 1 21 1 21 1 21 1 21 11 11\n1 1 1 1 121 1 1 1 1 1 1\n1 1 31 31 31 31 1 1 1 1 1 ", "output": "6 11 5 5 1 4"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CHaveYourCakeAndEatItToo"}}, "batch": {"id": "584616ee-65c9-4969-9a4e-da204fe65cf4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Have_Your_Cake_and_Eat_It_Too.cpp"}