{"name": "A1. Breaking The Rules (Easy Version)", "group": "Codeforces - NJACK CP Monthly Contest (August)", "url": "https://codeforces.com/gym/544601/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1724427418783, "input": "3\n1 2 1\n2 3 1\n5 2 10\n", "output": "YES\nNO\nYES\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "A1BreakingTheRulesEasyVersion"}}, "batch": {"id": "1b857295-4aa6-4930-9721-26ea8a92e4b3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_1_Breaking_The_Rules_Easy_Version.cpp"}