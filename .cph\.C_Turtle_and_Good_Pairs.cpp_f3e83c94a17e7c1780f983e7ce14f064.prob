{"name": "C. Turtle and Good Pairs", "group": "Codeforces - Codeforces Round 968 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2003/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1728135470932, "input": "5\n3\nabc\n5\nedddf\n6\nturtle\n8\npppppppp\n10\ncodeforces\n", "output": "acb\nddedf\nurtlet\npppppppp\ncodeforces\n"}, {"id": 1728138326071, "input": "1\n20\nexexxexeeexexxexxxex", "output": ""}, {"id": 1728187464578, "input": "1\n6\npppppp", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTurtleAndGoodPairs"}}, "batch": {"id": "425a3ebc-3a59-41e6-81cc-5e39ef46ede7", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Turtle_and_Good_Pairs.cpp"}