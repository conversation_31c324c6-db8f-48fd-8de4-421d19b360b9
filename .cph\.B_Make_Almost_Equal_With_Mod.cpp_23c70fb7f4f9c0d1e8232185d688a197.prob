{"name": "<PERSON><PERSON> Make Almost Equal With Mod", "group": "Codeforces - Pinely Round 3 (Div. 1 + Div. 2)", "url": "https://codeforces.com/problemset/problem/1909/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n4\n8 15 22 30\n5\n60 90 98 120 308\n6\n328 769 541 986 215 734\n5\n1000 2000 7000 11000 16000\n2\n2 1\n", "output": "7\n30\n3\n5000\n1000000000000000000\n", "id": 1721220290639}, {"id": 1721221489388, "input": "1\n3\n3 5 ", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMakeAlmostEqualWithMod"}}, "batch": {"id": "90dc3884-a1c0-46b2-81c8-4b86eff48c71", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Make_Almost_Equal_With_Mod.cpp"}