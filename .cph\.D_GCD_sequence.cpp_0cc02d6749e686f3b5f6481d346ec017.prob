{"name": "<PERSON><PERSON>CD-sequence", "group": "Codeforces - Codeforces Round 950 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1980/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1734358819055, "input": "12\n6\n20 6 12 3 48 36\n4\n12 6 3 4\n3\n10 12 3\n5\n32 16 8 4 2\n5\n100 50 2 10 20\n4\n2 4 8 1\n10\n7 4 6 2 4 5 1 4 2 8\n7\n5 9 6 8 5 9 2\n6\n11 14 8 12 9 3\n9\n5 7 3 10 6 3 12 6 3\n3\n4 2 4\n8\n1 6 11 12 6 12 3 6\n", "output": "YES\nNO\nYES\nNO\nYES\nYES\nNO\nYES\nYES\nYES\nYES\nYES\n"}, {"id": 1734360520162, "input": "1\n8\n1 6 11 12 6 12 3 6", "output": "YES"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DGCDSequence"}}, "batch": {"id": "4bd8c9e5-03f4-4810-8769-a9d462fb491d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_GCD_sequence.cpp"}