{"name": "B. Scale", "group": "Codeforces - Codeforces Round 962 (Div. 3)", "url": "https://codeforces.com/contest/1996/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1722005036437, "input": "4\n4 4\n0000\n0000\n0000\n0000\n6 3\n000111\n000111\n000111\n111000\n111000\n111000\n6 2\n001100\n001100\n111111\n111111\n110000\n110000\n8 1\n11111111\n11111111\n11111111\n11111111\n11111111\n11111111\n11111111\n11111111\n", "output": "0\n01\n10\n010\n111\n100\n11111111\n11111111\n11111111\n11111111\n11111111\n11111111\n11111111\n11111111\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BScale"}}, "batch": {"id": "ffac97ee-a9ea-4b0a-8263-5e67d8b1066e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Scale.cpp"}