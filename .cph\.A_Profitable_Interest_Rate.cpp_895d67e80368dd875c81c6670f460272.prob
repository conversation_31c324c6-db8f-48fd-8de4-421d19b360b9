{"name": "A. Profitable Interest Rate", "group": "Codeforces - Codeforces Round 980 (Div. 2)", "url": "https://codeforces.com/contest/2024/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n10 5\n7 9\n5 100\n1 1\n1 2\n", "output": "10\n5\n0\n1\n0\n", "id": 1729415134704}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AProfitableInterestRate"}}, "batch": {"id": "bd01156b-4edb-4806-8848-6340d4e3773d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Profitable_Interest_Rate.cpp"}