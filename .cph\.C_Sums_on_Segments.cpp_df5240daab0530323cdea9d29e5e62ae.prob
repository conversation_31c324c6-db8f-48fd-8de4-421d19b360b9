{"name": "<PERSON><PERSON> on Segments", "group": "Codeforces - Educational Codeforces Round 173 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2043/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n5\n1 -1 10 1 1\n5\n-1 -1 -1 -1 -1\n2\n-1 2\n2\n7 1\n3\n1 4 -1\n", "output": "8\n-1 0 1 2 9 10 11 12\n6\n-5 -4 -3 -2 -1 0\n4\n-1 0 1 2\n4\n0 1 7 8\n6\n-1 0 1 3 4 5\n", "id": 1736920625819}, {"id": 1736925632861, "input": "1\n5\n-1 -400813612 -1 -1 1", "output": "8"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CSumsOnSegments"}}, "batch": {"id": "a9b7a45f-3e18-4294-a39d-20b3fd4bf8e2", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Sums_on_Segments.cpp"}