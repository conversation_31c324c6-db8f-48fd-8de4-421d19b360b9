{"name": "<PERSON><PERSON> Conjecture", "group": "Codeforces - Codeforces Round 955 (Div. 2, with prizes from NEAR!)", "url": "https://codeforces.com/problemset/problem/1982/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1720025808838, "input": "13\n1 3 1\n2 3 1\n24 5 5\n16 3 2\n2 2 1\n1337 18 1\n1 2 144133\n12345678 3 10\n998244353 2 998244353\n998244353 123456789 998244352\n998244354 998241111 998244352\n998244355 2 9982443\n1000000000 1000000000 1000000000\n", "output": "2\n1\n1\n2\n3\n1338\n1\n16936\n1\n21180097\n6486\n1\n2\n"}, {"id": 1720068398666, "input": "1\n16 3 2", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BCollatzConjecture"}}, "batch": {"id": "5a7635fe-04c6-4a31-8cda-afcc1776f867", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Collatz_Conjecture.cpp"}