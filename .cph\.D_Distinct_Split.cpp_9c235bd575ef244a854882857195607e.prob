{"name": "D. Distinct Split", "group": "Codeforces - Codeforces Round 849 (Div. 4)", "url": "https://codeforces.com/contest/1791/problem/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "5\n2\naa\n7\nabcabcd\n5\naaaaa\n10\npaiumoment\n4\naazz\n", "output": "2\n7\n2\n10\n3\n", "id": 1733311727793}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DDistinctSplit"}}, "batch": {"id": "fdce54cc-8491-49d8-a615-ff601bf1699e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Distinct_Split.cpp"}