{"name": "<PERSON><PERSON> of Love", "group": "Codeforces - Codeforces Round 957 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1992/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720930606738, "input": "6\n6 2 0\nLWLLLW\n6 1 1\nLWLLLL\n6 1 1\nLWLLWL\n6 2 15\nLWLLCC\n6 10 0\nCCCCCC\n6 6 1\nWCCCCW\n", "output": "YES\nYES\nNO\nNO\nYES\nYES\n"}, {"id": 1720931090494, "input": "1\n6 1 1\nLWLLLL", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DTestOfLove"}}, "batch": {"id": "268e0e95-37c0-4189-8f46-b4825bf6064a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Test_of_Love.cpp"}