{"name": "A. Primary Task", "group": "Codeforces - Codeforces Round 966 (Div. 3)", "url": "https://codeforces.com/contest/2000/problem/A", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "7\n100\n1010\n101\n105\n2033\n1019\n1002\n", "output": "NO\nYES\nNO\nYES\nNO\nYES\nNO\n", "id": 1723565093795}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "APrimaryTask"}}, "batch": {"id": "1ca99bb4-1a27-4845-8d36-360115bf9bb4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Primary_Task.java"}