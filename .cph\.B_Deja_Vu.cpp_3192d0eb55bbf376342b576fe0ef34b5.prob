{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 907 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1891/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1717996279407, "input": "4\n\n5 3\n1 2 3 4 4\n2 3 4\n\n7 3\n7 8 12 36 48 6 3\n10 4 2\n\n5 4\n2 2 2 2 2\n1 1 1 1\n\n5 5\n1 2 4 8 16\n5 2 3 4 1\n", "output": "1 2 3 6 6\n7 10 14 38 58 6 3\n3 3 3 3 3\n1 3 7 11 19\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BDejaVu"}}, "batch": {"id": "3c255568-16ab-416f-9c45-88cef5b65b0a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Deja_Vu.cpp"}