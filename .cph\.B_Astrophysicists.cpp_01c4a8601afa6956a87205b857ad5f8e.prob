{"name": "B. Astrophysicists", "group": "Codeforces - Codeforces Round 880 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1836/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n3 3 100\n2 1 14\n91 2 13\n36 16 6\n73 8 22\n", "output": "100\n0\n26\n72\n176\n", "id": 1719724238790}, {"id": 1719725986911, "input": "2\n3 1 3\n3 2 3", "output": "3\n3"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BAstrophysicists"}}, "batch": {"id": "d8276504-749f-484d-a38e-7c4b0603a289", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Astrophysicists.cpp"}