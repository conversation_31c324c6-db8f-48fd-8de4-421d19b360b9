{"name": "<PERSON>. <PERSON> Game", "group": "Codeforces - EPIC Institute of Technology Round August 2024 (Div. 1 + Div. 2)", "url": "https://codeforces.com/contest/2002/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1723388233337, "input": "2\n2\n1 2\n1 2\n3\n1 2 3\n2 3 1\n", "output": "<PERSON>\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BRemovalsGame"}}, "batch": {"id": "889dbd97-21b4-44cb-960f-612d6c0a473a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Removals_Game.cpp"}