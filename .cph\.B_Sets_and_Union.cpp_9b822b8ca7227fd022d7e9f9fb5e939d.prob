{"name": "B. Sets and Union", "group": "Codeforces - Codeforces Round 899 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1882/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n3\n3 1 2 3\n2 4 5\n2 3 4\n4\n4 1 2 3 4\n3 2 5 6\n3 3 5 6\n3 4 5 6\n5\n1 1\n3 3 6 10\n1 9\n2 1 3\n3 5 8 9\n1\n2 4 28\n", "output": "4\n5\n6\n0\n", "id": 1733843818995}, {"id": 1733845605677, "input": "1\n3\n3 1 2 3\n2 4 5\n2 3 4", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BSetsAndUnion"}}, "batch": {"id": "cca8d476-1f86-4c35-ae17-b35a604740d9", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Sets_and_Union.cpp"}