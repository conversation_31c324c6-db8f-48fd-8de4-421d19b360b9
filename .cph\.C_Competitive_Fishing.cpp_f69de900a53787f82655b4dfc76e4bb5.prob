{"name": "C. Competitive Fishing", "group": "Codeforces - Educational Codeforces Round 172 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2042/problem/C", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1733152892591, "input": "7\n4 1\n1001\n4 1\n1010\n4 1\n0110\n4 2\n0110\n6 3\n001110\n10 20\n1111111111\n5 11\n11111\n", "output": "2\n-1\n2\n-1\n3\n4\n-1\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CCompetitiveFishing"}}, "batch": {"id": "550fe698-087a-419f-b420-aca8ea21a246", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Competitive_Fishing.cpp"}