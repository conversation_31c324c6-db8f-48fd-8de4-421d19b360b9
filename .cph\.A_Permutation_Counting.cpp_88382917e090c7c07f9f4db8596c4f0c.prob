{"name": "A. Permutation Counting", "group": "Codeforces - Codeforces Round 942 (Div. 1)", "url": "https://codeforces.com/problemset/problem/1967/A", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "8\n1 10\n1\n2 4\n8 4\n3 4\n6 1 8\n3 9\n7 6 2\n5 3\n6 6 7 4 6\n9 7\n7 6 1 7 6 2 4 3 3\n10 10\n1 3 1 2 1 9 3 5 7 5\n9 8\n5 8 7 5 1 3 2 9 8\n", "output": "11\n15\n15\n22\n28\n32\n28\n36\n", "id": 1734454585672}, {"id": 1734455385086, "input": "1\n3 4\n6 1 8", "output": "15"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "APermutationCounting"}}, "batch": {"id": "105c159d-f62d-41cc-baea-9671c554263e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Permutation_Counting.cpp"}