{"name": "A. Bonus Project", "group": "Codeforces - 2024-2025 ICPC, NERC, Southern and Volga Russian Regional Contest (Unrated, Online Mirror, ICPC Rules, Preferably Teams)", "url": "https://codeforces.com/problemset/problem/2038/A", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"input": "3 6\n4 7 6\n1 2 3\n", "output": "1 3 2\n", "id": 1733753839688}, {"input": "3 12\n4 7 6\n1 2 3\n", "output": "0 0 0\n", "id": 1733753839618}, {"input": "3 11\n6 7 8\n1 2 3\n", "output": "6 3 2\n", "id": 1733753839689}, {"id": 1733754789028, "input": "10 54\n80 30 115 230 87 184 174 12 81 160\n16 3 23 23 29 23 29 3 9 20", "output": "0 1 5 10 3 8 6 4 9 8 "}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ABonusProject"}}, "batch": {"id": "65b5a7d2-1c61-4b6a-940d-b32fd33cdb7b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Bonus_Project.cpp"}