{"name": "<PERSON><PERSON> of Unattractive Pairs", "group": "Codeforces - Codeforces Round 913 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1907/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "10\n4\naabc\n5\nabaca\n10\navbvvcvvvd\n7\nabcdefg\n5\ndabbb\n8\naacebeaa\n7\nbbbbacc\n6\ndacfcc\n6\nfdfcdc\n9\ndbdcfbbdc\n", "output": "0\n1\n2\n1\n1\n0\n1\n0\n0\n1\n", "id": 1720542039891}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CRemovalOfUnattractivePairs"}}, "batch": {"id": "21b7b02e-8d6b-4275-a374-4b542e31a331", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Removal_of_Unattractive_Pairs.cpp"}