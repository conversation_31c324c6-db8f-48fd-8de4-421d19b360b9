{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 882 (Div. 2)", "url": "https://codeforces.com/contest/1847/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n3\n1 2 3\n5\n2 3 1 5 2\n4\n5 7 12 6\n", "output": "1\n2\n1\n", "id": 1733316229265}, {"id": 1733316917159, "input": "1\n2 \n1 1", "output": "2"}, {"id": 1733318260093, "input": "1\n2\n2 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BHamonOdyssey"}}, "batch": {"id": "b176758e-375d-467e-88e2-61fe808a787c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Hamon_Odyssey.cpp"}