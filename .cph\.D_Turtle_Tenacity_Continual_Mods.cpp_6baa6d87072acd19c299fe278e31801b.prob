{"name": "<PERSON><PERSON> Tenacity: Continual Mods", "group": "Codeforces - Codeforces Round 929 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1933/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720412003487, "input": "8\n6\n1 2 3 4 5 6\n5\n3 3 3 3 3\n3\n2 2 3\n5\n1 1 2 3 7\n3\n1 2 2\n3\n1 1 2\n6\n5 2 10 10 10 2\n4\n3 6 9 3\n", "output": "YES\nNO\nYES\nNO\nYES\nNO\nYES\nNO\n"}, {"id": 1720412689334, "input": "1\n6\n5 2 10 10 10 2\n", "output": "YES"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DTurtleTenacityContinualMods"}}, "batch": {"id": "16d7237a-b7e7-4f3d-ac8c-1f933e2f3b12", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Turtle_Tenacity_Continual_Mods.cpp"}