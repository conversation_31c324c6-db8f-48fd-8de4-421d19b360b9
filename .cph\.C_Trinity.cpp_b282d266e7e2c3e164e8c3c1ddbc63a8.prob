{"name": "C. Trinity", "group": "Codeforces - Codeforces Round 983 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2032/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n7\n1 2 3 4 5 6 7\n3\n1 3 2\n3\n4 5 3\n15\n9 3 8 1 6 5 3 8 2 1 4 2 9 4 7\n", "output": "3\n1\n0\n8\n", "id": 1733924926617}, {"id": 1733931744674, "input": "1\n10\n260 112 465 320 296 12 197 87 271 153", "output": "4"}, {"id": 1733934515514, "input": "1\n10\n3 5 3 4 5 4 2 4 4 1", "output": "2"}, {"id": 1733935524309, "input": "1\n7\n1 2 3 4 5 6 7", "output": "3"}, {"id": 1733935729669, "input": "1\n15\n9 3 8 1 6 5 3 8 2 1 4 2 9 4 7", "output": "8"}, {"id": 1733935817451, "input": "1\n7\n1 2 3 4 5 6 7", "output": "3"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTrinity"}}, "batch": {"id": "f4ca3bbb-0ef2-4f46-8170-199e1babbcfc", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Trinity.cpp"}