{"name": "B. Substring and Subsequence", "group": "Codeforces - Educational Codeforces Round 167 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1989/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719727006610, "input": "5\naba\ncb\ner\ncf\nmmm\nmmm\ncontest\ntest\ncde\nabcefg\n", "output": "4\n4\n3\n7\n7\n"}, {"id": 1719985318723, "input": "1\nrrbibbjrirjrrejjbeirerejrrerejeeirejjeiierrieerrjierjerbebri\nejjiibjbbrbererriribeeeiirrbbbrirjrrejjeireejrreejeeiejjeiierrieerrjierebebrijereibriijrijrerrirebji", "output": "108"}, {"id": 1719987695601, "input": "2\nab\nbab\nab\naab\n", "output": "3\n3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BSubstringAndSubsequence"}}, "batch": {"id": "b840472d-030a-4bb5-8a96-b4730ba24db3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Substring_and_Subsequence.cpp"}