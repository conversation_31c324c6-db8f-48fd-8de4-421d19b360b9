{"name": "B. Transfusion", "group": "Codeforces - Codeforces Round 991 (Div. 3)", "url": "https://codeforces.com/contest/2050/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "8\n3\n3 2 1\n3\n1 1 3\n4\n1 2 5 4\n4\n1 6 6 1\n5\n6 2 1 4 2\n4\n1 4 2 1\n5\n3 1 2 1 3\n3\n2 4 2\n", "output": "YES\nNO\nYES\nNO\nYES\nNO\nNO\nNO\n", "id": 1733409780317}, {"id": 1733410214262, "input": "1\n5\n6 2 1 4 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BTransfusion"}}, "batch": {"id": "3f2010df-23c1-4969-acea-24a3dea166dd", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Transfusion.cpp"}