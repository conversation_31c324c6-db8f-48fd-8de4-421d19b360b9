{"name": "<PERSON><PERSON>, <PERSON><PERSON>, and the Permu<PERSON>", "group": "Codeforces - Codeforces Round 981 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2033/E", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "6\n5\n1 2 3 4 5\n5\n5 4 3 2 1\n5\n2 3 4 5 1\n4\n2 3 4 1\n3\n1 3 2\n7\n2 3 1 5 6 7 4\n", "output": "0\n0\n2\n1\n0\n2\n", "id": 1733763575015}, {"id": 1733764077690, "input": "1\n5\n2 3 4 5 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ESakurakoKosukeAndThePermutation"}}, "batch": {"id": "f64da43b-ccd7-4e75-9f59-801720d4a320", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\E_<PERSON><PERSON>_<PERSON>suke_and_the_Permutation.cpp"}