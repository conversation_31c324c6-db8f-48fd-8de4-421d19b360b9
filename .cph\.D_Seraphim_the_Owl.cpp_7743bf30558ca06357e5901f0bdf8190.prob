{"name": "<PERSON><PERSON> the Owl", "group": "Codeforces - Codeforces Round 935 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1945/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1729852841597, "input": "4\n4 2\n7 3 6 9\n4 3 8 5\n6 2\n6 9 7 1 8 3\n5 8 8 1 4 1\n7 7\n7 2 9 2 6 5 9\n9 1 10 7 1 4 9\n2 1\n2 3\n1 1\n", "output": "14\n22\n9\n3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DSeraphimTheOwl"}}, "batch": {"id": "ade627a4-039d-49c2-9e3f-22dbb622268d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Seraphim_the_Owl.cpp"}