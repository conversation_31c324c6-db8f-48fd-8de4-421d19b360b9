{"name": "C. Make it Alternating", "group": "Codeforces - Educational Codeforces Round 155 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1879/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "3\n10010\n111\n0101\n", "output": "1 2\n2 6\n0 1\n", "id": 1730817902827}, {"id": 1730818502290, "input": "1\n1100", "output": "2 8"}, {"id": 1730821326645, "input": "1\n1001", "output": "1 2"}, {"id": 1730821524112, "input": "1\n10011", "output": "2 8"}, {"id": 1730822364835, "input": "1\n11011", "output": "2 8"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CMakeItAlternating"}}, "batch": {"id": "98304598-c34f-4e70-91e8-a4cad40e259b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Make_it_Alternating.cpp"}