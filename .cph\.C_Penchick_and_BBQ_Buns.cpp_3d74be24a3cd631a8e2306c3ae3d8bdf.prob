{"name": "<PERSON><PERSON> and BBQ Buns", "group": "Codeforces - Codeforces Round 987 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2031/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "2\n3\n12\n", "output": "-1\n1 2 3 6 10 2 7 6 10 1 7 3\n", "id": 1733138559394}, {"id": 1733140014849, "input": "8\n27\n28\n29\n30\n31\n32\n33\n34", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CPenchickAndBBQBuns"}}, "batch": {"id": "aacc52bf-923a-4dd5-8dad-e314fe84299d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Penchick_and_BBQ_Buns.cpp"}