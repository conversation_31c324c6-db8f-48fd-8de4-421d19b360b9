{"name": "B. XOR Sequences", "group": "Codeforces - Codeforces Round 951 (Div. 2)", "url": "https://codeforces.com/contest/1979/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1717686762218, "input": "4\n0 1\n12 4\n57 37\n316560849 14570961\n", "output": "1\n8\n4\n33554432\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BXORSequences"}}, "batch": {"id": "8bbe3110-16fe-4faf-86cd-1f103c6b67cb", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_XOR_Sequences.cpp"}