{"name": "<PERSON><PERSON> Training Before the Olympiad", "group": "Codeforces - Good Bye 2023", "url": "https://codeforces.com/problemset/problem/1916/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "4\n1\n31\n6\n6 3 7 2 5 4\n3\n3 10 11\n5\n7 13 11 19 1\n", "output": "31\n6 8 16 18 22 26\n3 12 24\n7 20 30 48 50\n", "id": 1720526976815}, {"id": 1720530213103, "input": "1\n6\n6 3 7 2 5 4", "output": "6 8 16 18 22 26"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTrainingBeforeTheOlympiad"}}, "batch": {"id": "0c1f0090-5319-41d8-877b-e44976a3228a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Training_Before_the_Olympiad.cpp"}