{"name": "B. Black Cells", "group": "Codeforces - Educational Codeforces Round 171 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2026/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n2\n1 2\n1\n7\n3\n2 4 9\n5\n1 5 8 10 13\n", "output": "1\n1\n2\n3\n", "id": 1730127045317}, {"id": 1730127582561, "input": "1\n7\n1 6 7 10 50 100 150", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BBlackCells"}}, "batch": {"id": "4144d0e5-01f0-45ba-9cfd-08c1b5201ec4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Black_Cells.cpp"}