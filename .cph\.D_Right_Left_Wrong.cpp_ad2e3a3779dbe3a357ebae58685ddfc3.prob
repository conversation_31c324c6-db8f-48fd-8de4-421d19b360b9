{"name": "<PERSON><PERSON> Right Left Wrong", "group": "Codeforces - Codeforces Round 966 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2000/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1728346411848, "input": "4\n6\n3 5 1 4 3 2\nLRLLLR\n2\n2 8\nLR\n2\n3 9\nRL\n5\n1 2 3 4 5\nLRLRR\n", "output": "18\n10\n0\n22\n"}, {"id": 1728376613590, "input": "1\n7\n5 1 5 2 1 2 4\nRRRRRLL", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DRightLeftWrong"}}, "batch": {"id": "ca676e35-be76-4675-a616-92022aee0dac", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Right_Left_Wrong.cpp"}