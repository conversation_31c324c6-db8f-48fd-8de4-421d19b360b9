{"name": "F. Money Trees", "group": "Codeforces - Codeforces Round 898 (Div. 4)", "url": "https://codeforces.com/problemset/problem/1873/F", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730825474160, "input": "5\n5 12\n3 2 4 1 8\n4 4 2 4 1\n4 8\n5 4 1 2\n6 2 3 1\n3 12\n7 9 10\n2 2 4\n1 10\n11\n1\n7 10\n2 6 3 1 5 10 6\n72 24 24 12 4 4 2\n", "output": "3\n2\n1\n0\n3\n"}, {"id": 1730826641056, "input": "1\n2 5\n5 8\n48 6", "output": "1"}, {"id": 1730827971358, "input": "1\n1 3\n3\n4", "output": "1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "FMoneyTrees"}}, "batch": {"id": "185ae2df-5b8c-471d-888c-bcac2e03c262", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\F_Money_Trees.cpp"}