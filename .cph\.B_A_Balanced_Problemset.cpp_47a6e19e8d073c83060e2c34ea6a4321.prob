{"name": "<PERSON>. A Balanced Problemset?", "group": "Codeforces - Codeforces Round 921 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1925/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1500, "tests": [{"id": 1721709988159, "input": "3\n10 3\n5 5\n420 69\n", "output": "2\n1\n6\n"}, {"id": 1721711570980, "input": "1\n2 1", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BABalancedProblemset"}}, "batch": {"id": "29677947-e65a-4664-becc-9e6e63edf2a3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_A_Balanced_Problemset.cpp"}