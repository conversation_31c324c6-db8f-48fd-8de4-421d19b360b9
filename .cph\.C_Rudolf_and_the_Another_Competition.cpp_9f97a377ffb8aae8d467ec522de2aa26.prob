{"name": "<PERSON><PERSON> and the Another Competition", "group": "Codeforces - Codeforces Round 883 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1846/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1722438626191, "input": "5\n3 3 120\n20 15 110\n90 90 100\n40 40 40\n2 1 120\n30\n30\n1 3 120\n10 20 30\n3 2 27\n8 9\n10 7\n10 8\n3 3 15\n7 2 6\n7 5 4\n1 9 8\n", "output": "2\n1\n1\n2\n1\n"}, {"id": 1722439346618, "input": "1\n3 2 27\n8 9\n10 7\n10 8\n", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CRudolfAndTheAnotherCompetition"}}, "batch": {"id": "8f77ee3a-a4cb-4c59-9c9a-87ae38b48577", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Rudolf_and_the_Another_Competition.cpp"}