{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 977 (Div. 2, based on COMPFEST 16 - Final Round)", "url": "https://codeforces.com/contest/2021/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n6 3\n0 3 2 1 5 2\n6 2\n1 3 4 1 0 2\n4 5\n2 5 10 3\n", "output": "4\n6\n0\n", "id": 1728196402303}, {"id": 1728196864542, "input": "1\n6 2\n1 3 4 1 0 2", "output": "6"}, {"id": 1728199454245, "input": "1\n6 100000\n0 1 1 1 2 4", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMaximizeMex"}}, "batch": {"id": "a446a7f2-7beb-4a82-bc6a-bf7ef56f5a7c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Maximize_Mex.cpp"}