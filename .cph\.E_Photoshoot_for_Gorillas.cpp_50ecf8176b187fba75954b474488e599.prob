{"name": "<PERSON><PERSON> for Gorillas", "group": "Codeforces - Codeforces Round 966 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2000/E", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1734237610368, "input": "5\n3 4 2\n9\n1 1 1 1 1 1 1 1 1\n2 1 1\n2\n5 7\n20 15 7\n9\n4 1 4 5 6 1 1000000000 898 777\n1984 1 1\n4\n5 4 1499 2004\n9 5 5\n6\n6 7 14 16 16 6\n", "output": "21\n12\n49000083104\n3512\n319\n"}, {"id": 1734239251512, "input": "1\n9 5 5\n6\n6 7 14 16 16 6", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "EPhotoshootForGorillas"}}, "batch": {"id": "79e61b95-8334-42d2-b479-e6cc4fc0ea43", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\E_Photoshoot_for_Gorillas.cpp"}