{"name": "C_A2. Breaking The Rules (Hard Version)", "group": "Codeforces - NJACK CP Monthly Contest (August)", "url": "https://codeforces.com/gym/544601/problem/C_A2", "interactive": false, "memoryLimit": 256, "timeLimit": 4000, "tests": [{"id": 1724429406535, "input": "3\n1 1 1\n2 3 1\n100 50 50\n", "output": "YES\nYES\nYES\n"}, {"id": 1724429769132, "input": "1\n6 6 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CA2BreakingTheRulesHardVersion"}}, "batch": {"id": "1016788e-d532-44e5-899d-70df533d1ba3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_A_2_Breaking_The_Rules_Hard_Version.cpp"}