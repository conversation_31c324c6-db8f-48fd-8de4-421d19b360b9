{"name": "<PERSON><PERSON>'s Adventures in Permuting", "group": "Codeforces - Codeforces Round 986 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2028/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1733939805541, "input": "7\n10 1 0\n1 2 3\n100 2 1\n3 0 1\n3 0 0\n1000000000000000000 0 0\n1000000000000000000 1000000000000000000 1000000000000000000\n", "output": "0\n1\n50\n2\n-1\n-1\n1000000000000000000\n"}, {"id": 1733940619948, "input": "1\n4 0 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BAlicesAdventuresInPermuting"}}, "batch": {"id": "f1447bef-3518-4e71-be3f-5ac772a7f636", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Alice_s_Adventures_in_Permuting.cpp"}