{"name": "<PERSON><PERSON>'s Box", "group": "Codeforces - Codeforces Round 970 (Div. 3)", "url": "https://codeforces.com/problemset/problem/2008/F", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1734154757374, "input": "3\n3\n3 2 3\n4\n2 2 2 4\n5\n1 2 3 4 5\n", "output": "7\n6\n500000012\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "FSakurakosBox"}}, "batch": {"id": "659e9a21-6105-44c1-8e54-22a5b7c130a9", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\F_Sakurako_s_Box.cpp"}