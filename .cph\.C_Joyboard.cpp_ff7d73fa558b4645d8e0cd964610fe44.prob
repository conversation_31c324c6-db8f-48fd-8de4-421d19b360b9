{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 902 (Div. 2, based on COMPFEST 15 - Final Round)", "url": "https://codeforces.com/problemset/problem/1877/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1720624088759, "input": "4\n4 6 3\n2 0 1\n265 265 265\n3 10 2\n", "output": "2\n1\n0\n5\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CJoyboard"}}, "batch": {"id": "5edfbb7d-b9a4-46bf-9a56-701f166b6864", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Joyboard.cpp"}