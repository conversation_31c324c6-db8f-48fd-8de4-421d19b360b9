{"name": "C. Insert Zero and Invert Prefix", "group": "Codeforces - Codeforces Round 876 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1839/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n5\n1 1 0 0 0\n1\n1\n3\n0 1 1\n6\n1 0 0 1 1 0\n", "output": "YES\n0 0 2 1 3\nNO\nNO\nYES\n0 1 0 2 4 2\n", "id": 1733464792173}, {"id": 1733465437683, "input": "19\n12\n1 0 1 0 0 1 1 0 1 1 0 0\n9\n1 1 0 1 0 0 0 1 0\n12\n0 0 0 1 1 0 1 1 0 1 0 1\n7\n0 1 0 0 0 0 0\n11\n1 0 0 1 1 1 0 1 0 1 1\n10\n0 0 0 0 1 0 0 0 0 1\n12\n0 0 0 0 1 0 1 0 0 0 0 1\n11\n0 0 1 0 0 1 0 0 1 1 1\n12\n1 0 0 1 0 0 0 1 1 0 0 1\n12\n1 1 1 0 0 1 0 0 1 0 0 1\n12\n0 0 0 1 1 0 0 1 1 1 0 0\n11\n1 0 0 0 0 1 1 1 1 0 0\n10\n1 0 0 0 0 1 0 1 1 1\n12\n0 1 0 1 0 1 0 0 1 0 0 1\n11\n0 1 0 0 0 1 1 1 0 1 1\n12\n1 1 1 1 0 1 1 1 1 1 0 0\n12\n0 1 0 1 1 1 1 1 0 1 0 0\n10\n0 1 1 1 1 0 0 0 1 1\n8\n0 0 0 1 1 0 1 0", "output": ""}, {"id": 1733465774725, "input": "1\n5\n0 0 0 0 0", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CInsertZeroAndInvertPrefix"}}, "batch": {"id": "11a3ae58-7784-46d1-8391-65a1874fd72b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Insert_Zero_and_Invert_Prefix.cpp"}