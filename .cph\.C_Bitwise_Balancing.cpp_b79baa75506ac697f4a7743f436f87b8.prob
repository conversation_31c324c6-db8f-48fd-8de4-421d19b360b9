{"name": "<PERSON><PERSON> Balancing", "group": "Codeforces - Codeforces Round 976 (Div. 2) and Divide By Zero 9.0", "url": "https://codeforces.com/problemset/problem/2020/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "3\n2 2 2\n4 2 6\n10 2 14\n", "output": "0\n-1\n12\n", "id": 1733942857376}, {"id": 1733943668494, "input": "1\n1 0 0", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CBitwiseBalancing"}}, "batch": {"id": "f7ce5c1a-ea73-4f79-8bdc-4eb62fbbdf7d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Bitwise_Balancing.cpp"}