{"name": "<PERSON><PERSON> and Game", "group": "Codeforces - Codeforces Round 901 (Div. 1)", "url": "https://codeforces.com/problemset/problem/1874/A", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "4\n2 2 1\n1 2\n3 4\n1 1 10000\n1\n2\n4 5 11037\n1 1 4 5\n1 9 1 9 8\n1 1 1\n2\n1\n", "output": "6\n1\n19\n2\n", "id": 1720627362986}, {"id": 1720628274839, "input": "1\n5 4 1\n807033707 969121316 442675026 807033707 807033707\n234037871 486985454 368985648 39735900", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AJellyfishAndGame"}}, "batch": {"id": "d3a07791-a02f-4dd0-928b-42a12cfca3d4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Jellyfish_and_Game.cpp"}