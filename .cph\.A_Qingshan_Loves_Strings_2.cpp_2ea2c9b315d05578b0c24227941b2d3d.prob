{"name": "<PERSON><PERSON> Strings 2", "group": "Codeforces - Codeforces Round 906 (Div. 1)", "url": "https://codeforces.com/problemset/problem/1889/A", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "6\n2\n01\n3\n000\n4\n1111\n6\n001110\n10\n0111001100\n3\n001\n", "output": "0\n\n-1\n-1\n2\n6 7\n1\n10\n-1\n", "id": 1733141408768}, {"id": 1733142680525, "input": "1\n8\n10110001", "output": "2\n6 7"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AQingshanLovesStrings2"}}, "batch": {"id": "c509811d-0b09-4c52-8351-1ff39a1aa534", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Qingshan_Loves_Strings_2.cpp"}