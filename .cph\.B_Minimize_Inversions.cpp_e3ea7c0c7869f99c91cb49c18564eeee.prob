{"name": "B. Minimize Inversions", "group": "Codeforces - Codeforces Round 922 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1918/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1717673085364, "input": "3\n5\n1 2 3 4 5\n5 4 3 2 1\n3\n3 1 2\n3 1 2\n6\n2 5 6 1 3 4\n1 5 3 6 2 4\n", "output": "3 2 5 1 4\n3 4 1 5 2\n1 2 3\n1 2 3\n2 3 4 6 5 1\n1 2 4 3 5 6\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMinimizeInversions"}}, "batch": {"id": "28b0a253-37ec-44b3-9777-6a5406978d12", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Minimize_Inversions.cpp"}