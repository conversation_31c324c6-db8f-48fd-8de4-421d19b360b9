{"name": "B. Split Sort", "group": "Codeforces - Pinely Round 2 (Div. 1 + Div. 2)", "url": "https://codeforces.com/problemset/problem/1863/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719424526101, "input": "5\n1\n1\n2\n2 1\n6\n6 4 3 5 2 1\n3\n3 1 2\n19\n10 19 7 1 17 11 8 5 12 9 4 18 14 2 6 15 3 16 13\n", "output": "0\n1\n4\n1\n7\n"}, {"id": 1719424715284, "input": "1\n19\n10 19 7 1 17 11 8 5 12 9 4 18 14 2 6 15 3 16 13", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BSplitSort"}}, "batch": {"id": "99838324-1e0c-4b71-b9d1-95e67ad46915", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Split_Sort.cpp"}