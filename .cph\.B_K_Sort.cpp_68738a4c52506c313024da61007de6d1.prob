{"name": "B. K-Sort", "group": "Codeforces - EPIC Institute of Technology Round Summer 2024 (Div. 1 + Div. 2)", "url": "https://codeforces.com/contest/1987/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1719765509141, "input": "4\n3\n1 1 2\n2\n3 1\n1\n9\n5\n7 4 4 3 2\n", "output": "4\n3\n9\n7\n"}, {"id": 1719765560054, "input": "5\n3 \n1 1 1\n7 \n1 2 4 5 1 6 9\n6 \n9 9 9 9 9 9\n7 \n1 3 4 5 1 2 4\n6\n2 4 5 4 1 3\n", "output": "3\n15\n14\n10\n8\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BKSort"}}, "batch": {"id": "b586330d-ab5c-464c-9a4f-b35c1d7e0f23", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_K_Sort.cpp"}