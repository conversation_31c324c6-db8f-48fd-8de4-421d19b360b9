{"name": "D. Vlad and Division", "group": "Codeforces - Codeforces Round 928 (Div. 4)", "url": "https://codeforces.com/problemset/problem/1926/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730445042829, "input": "9\n4\n1 4 3 4\n2\n0 2147483647\n5\n476319172 261956880 2136179468 1671164475 1885526767\n3\n1335890506 811593141 1128223362\n4\n688873446 627404104 1520079543 1458610201\n4\n61545621 2085938026 1269342732 1430258575\n4\n0 0 2147483647 2147483647\n3\n0 0 2147483647\n8\n1858058912 289424735 1858058912 2024818580 1858058912 289424735 122665067 289424735\n", "output": "4\n1\n3\n2\n2\n3\n2\n2\n4\n"}, {"id": 1730446605666, "input": "1\n4\n0 0 2147483647 2147483647", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DVladAndDivision"}}, "batch": {"id": "ac3af24f-b418-45a2-90ec-040c2d7691cb", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Vlad_and_Division.cpp"}