{"name": "D. Inaccurate Subsequence Search", "group": "Codeforces - Codeforces Round 938 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1955/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1735535810162, "input": "5\n7 4 2\n4 1 2 3 4 5 6\n1 2 3 4\n7 4 3\n4 1 2 3 4 5 6\n1 2 3 4\n7 4 4\n4 1 2 3 4 5 6\n1 2 3 4\n11 5 3\n9 9 2 2 10 9 7 6 3 6 3\n6 9 7 8 10\n4 1 1\n4 1 5 6\n6\n", "output": "4\n3\n2\n4\n1\n"}, {"id": 1735539956646, "input": "1\n4 3 2\n9 9 10 3\n6 9 10", "output": "2"}, {"id": 1735543348571, "input": "1\n11 5 3\n9 9 2 2 10 9 7 6 3 6 3\n6 9 7 8 10", "output": "4"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DInaccurateSubsequenceSearch"}}, "batch": {"id": "c6852442-c5f6-4d0d-b1a1-c2e2ce60d5e3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Inaccurate_Subsequence_Search.cpp"}