{"name": "C. Inhabitant of the Deep Sea", "group": "Codeforces - Codeforces Round 938 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1955/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1729852013766, "input": "6\n4 5\n1 2 4 3\n4 6\n1 2 4 3\n5 20\n2 7 1 8 2\n2 2\n3 2\n2 15\n1 5\n2 7\n5 2\n", "output": "2\n3\n5\n0\n2\n2\n"}, {"id": 1729852378503, "input": "1\n4 5\n1 2 4 3", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CInhabitantOfTheDeepSea"}}, "batch": {"id": "9e440d28-8eb5-4fde-9a5c-b04ae8214c5f", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Inhabitant_of_the_Deep_Sea.cpp"}