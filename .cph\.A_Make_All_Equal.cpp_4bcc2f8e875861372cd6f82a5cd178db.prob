{"name": "<PERSON><PERSON> Make All Equal", "group": "Codeforces - Codeforces Round 967 (Div. 2)", "url": "https://codeforces.com/contest/2001/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1724164652310, "input": "7\n1\n1\n3\n1 2 3\n3\n1 2 2\n5\n5 4 3 2 1\n6\n1 1 2 2 3 3\n8\n8 7 6 3 8 7 6 3\n6\n1 1 4 5 1 4\n", "output": "0\n2\n1\n4\n4\n6\n3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AMakeAllEqual"}}, "batch": {"id": "d406e6b2-9539-4a9f-9ed1-22bf5e8c7a23", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Make_All_Equal.cpp"}