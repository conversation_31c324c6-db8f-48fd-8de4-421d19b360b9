{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 982 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2027/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1734075106704, "input": "6\n7\n3 6 4 9 2 5 2\n5\n5 4 4 2 2\n8\n2 2 4 4 6 6 10 10\n1\n1000\n9\n6 8 9 10 12 9 7 5 4\n7\n300000000 600000000 400000000 900000000 200000000 400000000 200000000\n", "output": "2\n0\n6\n0\n4\n2\n"}, {"id": 1734078755002, "input": "1\n4\n1 1 3 2", "output": "2"}, {"id": 1734080930053, "input": "1\n3\n2 1 2", "output": "0"}, {"id": 1734081235740, "input": "1\n4 \n1 2 1 2", "output": "1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BStalinSort"}}, "batch": {"id": "980a5aab-9959-4327-9198-fe77a956aab0", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Stalin_Sort.cpp"}