{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 953 (Div. 2)", "url": "https://codeforces.com/contest/1978/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "7\n4 4 5\n5 5 9\n10 10 5\n5 5 11\n1000000000 1000000000 1000000000\n1000000000 1000000000 1\n1000 1 1000\n", "output": "17\n35\n100\n45\n1000000000000000000\n1000000000000000000\n500500\n", "id": 1718529237504}, {"id": 1718529667320, "input": "1\n5 5 11", "output": "45"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BNewBakery"}}, "batch": {"id": "55fe51d4-4a99-4ed2-9aec-e5c3938e50a0", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_New_Bakery.cpp"}