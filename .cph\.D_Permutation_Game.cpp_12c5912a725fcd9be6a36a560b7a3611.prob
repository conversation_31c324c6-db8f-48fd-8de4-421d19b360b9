{"name": "D. Permutation Game", "group": "Codeforces - Codeforces Round 943 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1968/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1729513241627, "input": "10\n4 2 3 2\n4 1 2 3\n7 2 5 6\n10 8 2 10\n3 1 4 5 2 7 8 10 6 9\n5 10 5 1 3 7 10 15 4 3\n2 1000000000 1 2\n1 2\n4 4\n8 10 4 1\n5 1 4 3 2 8 6 7\n1 1 2 1 2 100 101 102\n5 1 2 5\n1 2 4 5 3\n4 6 9 4 2\n4 2 3 1\n4 1 3 2\n6 8 5 3\n6 9 5 4\n6 1 3 5 2 4\n6 9 8 9 5 10\n4 8 4 2\n2 3 4 1\n5 2 8 7\n4 2 3 1\n4 1 3 2\n6 8 5 3\n2 1000000000 1 2\n1 2\n1000000000 2\n", "output": "<PERSON><PERSON>\nDraw\nDraw\n<PERSON><PERSON>\nSasha\nSasha\nSasha\n<PERSON>\n"}, {"id": 1729514034160, "input": "1\n4 2 3 2\n4 1 2 3\n7 2 5 6", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DPermutationGame"}}, "batch": {"id": "ee37fe2b-f43d-4322-adb4-4b4611185231", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Permutation_Game.cpp"}