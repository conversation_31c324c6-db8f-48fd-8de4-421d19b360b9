{"name": "B. Go<PERSON> and the Exam", "group": "Codeforces - Hello 2025", "url": "https://codeforces.com/contest/2057/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "6\n1 0\n48843\n3 1\n2 3 2\n5 3\n1 2 3 4 5\n7 0\n4 7 1 3 2 4 1\n11 4\n3 2 1 4 4 3 4 2 1 3 3\n5 5\n1 2 3 4 5\n", "output": "1\n1\n2\n5\n2\n1\n", "id": 1736001946531}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BGorillaAndTheExam"}}, "batch": {"id": "6e65bbf7-3c45-42c7-9d6a-2049d9e142b9", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Gorilla_and_the_Exam.cpp"}