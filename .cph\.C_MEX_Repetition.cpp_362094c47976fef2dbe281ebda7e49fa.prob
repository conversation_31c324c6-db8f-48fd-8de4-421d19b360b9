{"name": "C. MEX Repetition", "group": "Codeforces - Pinely Round 2 (Div. 1 + Div. 2)", "url": "https://codeforces.com/problemset/problem/1863/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1718434128522, "input": "5\n1 2\n1\n3 1\n0 1 3\n2 2\n0 2\n5 5\n1 2 3 4 5\n10 100\n5 3 0 4 2 1 6 9 10 8\n", "output": "1\n2 0 1\n2 1\n2 3 4 5 0\n7 5 3 0 4 2 1 6 9 10\n"}, {"id": 1718435456031, "input": "1\n5 6\n1 2 3 4 5", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CMEXRepetition"}}, "batch": {"id": "57298a3c-6300-46ec-b109-af72a783d225", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_MEX_Repetition.cpp"}