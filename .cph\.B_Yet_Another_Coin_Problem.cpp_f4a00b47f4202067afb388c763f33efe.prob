{"name": "<PERSON>. Yet Another Coin Problem", "group": "Codeforces - Codeforces Round 931 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1934/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "14\n1\n2\n3\n5\n7\n11\n12\n14\n16\n17\n18\n20\n98\n402931328\n", "output": "1\n2\n1\n3\n2\n2\n2\n3\n2\n3\n2\n2\n8\n26862090\n", "id": 1720359669213}, {"id": 1720361744576, "input": "1\n5", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BYetAnotherCoinProblem"}}, "batch": {"id": "ee2e98d0-b166-46e1-ba56-5ce00d04a5f9", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Yet_Another_Coin_Problem.cpp"}