{"name": "<PERSON>. Equalize", "group": "Codeforces - Codeforces Round 924 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1928/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1721716955996, "input": "7\n2\n1 2\n4\n7 1 4 1\n3\n103 102 104\n5\n1 101 1 100 1\n5\n1 10 100 1000 1\n2\n3 1\n3\n1000000000 999999997 999999999\n", "output": "2\n2\n3\n2\n1\n1\n2\n"}, {"id": 1721838302992, "input": "1\n2\n1 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BEqualize"}}, "batch": {"id": "b6440c91-5669-4ee4-a285-4afe150132c3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Equalize.cpp"}