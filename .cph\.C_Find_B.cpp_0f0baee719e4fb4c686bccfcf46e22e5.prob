{"name": "<PERSON><PERSON>", "group": "Codeforces - Educational Codeforces Round 162 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1923/C", "interactive": false, "memoryLimit": 256, "timeLimit": 3000, "tests": [{"id": 1736837392505, "input": "1\n5 4\n1 2 1 4 5\n1 5\n4 4\n3 4\n1 3\n", "output": "YES\nNO\nYES\nNO\n"}, {"id": 1736846874979, "input": "1\n5 1\n1 2 1 4 5\n1 3\n", "output": "NO"}, {"id": 1736913104219, "input": "1\n7 1\n1 2 1 1 3 2 2 \n3 5", "output": "YES"}, {"id": 1736913446122, "input": "1\n8 1 \n3 1 1 1 3 6 6 8\n2 5", "output": "NO"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CFindB"}}, "batch": {"id": "aab49481-ae04-4fda-9870-b731cb9f4086", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Find_B.cpp"}