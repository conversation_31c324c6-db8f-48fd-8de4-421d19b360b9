{"name": "E. Building an Aquarium", "group": "Codeforces - Codeforces Round 898 (Div. 4)", "url": "https://codeforces.com/problemset/problem/1873/E", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719378773763, "input": "5\n7 9\n3 1 2 4 6 2 5\n3 10\n1 1 1\n4 1\n1 4 3 4\n6 1984\n2 6 5 9 1 8\n1 1000000000\n1\n", "output": "4\n4\n2\n335\n1000000001\n"}, {"id": 1719380032177, "input": "1\n4 1\n1 4 3 4", "output": "2"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "EBuildingAnAquarium"}}, "batch": {"id": "73de0b8c-2a37-42bc-859f-59b9c98785c7", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\E_Building_an_Aquarium.cpp"}