{"name": "L. <PERSON> Renovation", "group": "Codeforces - 2024-2025 ICPC, NERC, Southern and Volga Russian Regional Contest (Unrated, Online Mirror, ICPC Rules, Preferably Teams)", "url": "https://codeforces.com/problemset/problem/2038/L", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"input": "1\n", "output": "2\n", "id": 1733546855728}, {"input": "3\n", "output": "4\n", "id": 1733546855764}, {"input": "1000\n", "output": "1167\n", "id": 1733546855755}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "LBridgeRenovation"}}, "batch": {"id": "2864aa24-4616-4bb5-906b-15fd9ef34107", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\L_Bridge_Renovation.cpp"}