{"name": "<PERSON><PERSON> Question <PERSON>", "group": "Codeforces - Codeforces Round 963 (Div. 2)", "url": "https://codeforces.com/contest/1993/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1722782612713, "input": "6\n1\nABCD\n2\nAAAAAAAA\n2\nAAAABBBB\n2\n????????\n3\nABCABCABCABC\n5\nACADC??ACAC?DCAABC?C\n", "output": "4\n2\n4\n0\n9\n13\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AQuestionMarks"}}, "batch": {"id": "ea566cba-474e-4679-bde1-e36fbb66bb71", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Question_Marks.cpp"}