{"name": "<PERSON><PERSON> K Distinct Points with Fixed Center", "group": "Codeforces - Codeforces Round 965 (Div. 2)", "url": "https://codeforces.com/contest/1998/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1723300764814, "input": "4\n10 10 1\n0 0 3\n-5 -8 8\n4 -5 3\n", "output": "10 10\n-1 -1\n5 -1\n-4 2\n-6 -7\n-5 -7\n-4 -7\n-4 -8\n-4 -9\n-5 -9\n-6 -9\n-6 -8\n1000 -1000\n-996 995\n8 -10\n"}, {"id": 1723301034155, "input": "1\n1 1 2", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AFindKDistinctPointsWithFixedCenter"}}, "batch": {"id": "bf8d639c-ae99-4813-8a15-7a9c012bdf26", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Find_K_Distinct_Points_with_Fixed_Center.cpp"}