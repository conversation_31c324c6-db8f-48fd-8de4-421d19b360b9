{"name": "<PERSON><PERSON> Arrangement", "group": "Codeforces - Codeforces Round 982 (Div. 2)", "url": "https://codeforces.com/contest/2027/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1729953516704, "input": "5\n5\n1 5\n2 4\n3 3\n4 2\n5 1\n3\n2 2\n1 1\n1 2\n1\n3 2\n3\n100 100\n100 100\n100 100\n4\n1 4\n2 3\n1 5\n3 2\n", "output": "20\n8\n10\n400\n16\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ARectangleArrangement"}}, "batch": {"id": "1e0a1c21-a31d-405d-b44c-2422b68d5255", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Rectangle_Arrangement.cpp"}