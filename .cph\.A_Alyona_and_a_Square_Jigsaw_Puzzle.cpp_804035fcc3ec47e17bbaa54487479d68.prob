{"name": "<PERSON><PERSON> and a <PERSON> Jigsaw Puzzle", "group": "Codeforces - Codeforces Round 990 (Div. 2)", "url": "https://codeforces.com/contest/2047/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1733207382207, "input": "5\n1\n1\n2\n1 8\n5\n1 3 2 1 2\n7\n1 2 1 10 2 7 2\n14\n1 10 10 100 1 1 10 1 10 2 10 2 10 1\n", "output": "1\n2\n2\n2\n3\n"}, {"id": 1733207608918, "input": "1 14\n1 10 10 100 1 1 10 1 10 2 10 2 10 1", "output": "3"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AAlyonaAndASquareJigsawPuzzle"}}, "batch": {"id": "cfb0b345-3a2f-4a3e-9dc2-ca4932dd949a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Alyona_and_a_Square_Jigsaw_Puzzle.cpp"}