{"name": "A. <PERSON> Table", "group": "Codeforces - Hello 2025", "url": "https://codeforces.com/contest/2057/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n1 1\n2 2\n3 5\n", "output": "2\n3\n6\n", "id": 1736001656916}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AMEXTable"}}, "batch": {"id": "ae7394cf-e8aa-49d1-a650-f3e9aa0d08f1", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_MEX_Table.cpp"}