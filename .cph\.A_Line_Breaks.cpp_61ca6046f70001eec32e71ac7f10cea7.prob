{"name": "<PERSON><PERSON> Breaks", "group": "Codeforces - Codeforces Round 991 (Div. 3)", "url": "https://codeforces.com/contest/2050/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "5\n3 1\na\nb\nc\n2 9\nalpha\nbeta\n4 12\nhello\nworld\nand\ncodeforces\n3 2\nab\nc\nd\n3 2\nabc\nab\na\n", "output": "1\n2\n2\n1\n0\n", "id": 1733409400695}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ALineBreaks"}}, "batch": {"id": "ed61387a-6980-4a00-b75c-8f43f0195a8c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Line_Breaks.cpp"}