{"name": "<PERSON>. Fancy Coins", "group": "Codeforces - Educational Codeforces Round 153 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1860/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720938437988, "input": "4\n11 3 0 0\n11 3 20 20\n11 3 6 1\n100000000 2 0 0\n", "output": "5\n0\n1\n50000000\n"}, {"id": 1720968774139, "input": "1\n5 4 2 0", "output": "1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BFancyCoins"}}, "batch": {"id": "2e4bc910-3e48-42b3-81c6-6eee1899cf72", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Fancy_Coins.cpp"}