{"name": "C. <PERSON> Square", "group": "Codeforces - Codeforces Round 903 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1881/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720616171520, "input": "5\n4\nabba\nbcbb\nbccb\nabba\n2\nab\nba\n6\ncodefo\nrcesco\ndeforc\nescode\nforces\ncodefo\n4\nbaaa\nabba\nbaba\nbaab\n4\nbbaa\nabba\naaba\nabba\n", "output": "1\n2\n181\n5\n9\n"}, {"id": 1720619806354, "input": "1\n6\ncodefo\nrcesco\ndeforc\nescode\nforces\ncodefo", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CPerfectSquare"}}, "batch": {"id": "309ba27e-4764-453e-8b73-e6656c7f395a", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Perfect_Square.cpp"}