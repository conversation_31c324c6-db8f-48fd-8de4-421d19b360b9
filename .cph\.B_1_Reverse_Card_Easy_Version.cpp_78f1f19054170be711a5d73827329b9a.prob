{"name": "B1. Reverse Card (Easy Version)", "group": "Codeforces - Codeforces Round 942 (Div. 1)", "url": "https://codeforces.com/problemset/problem/1967/B1", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "6\n1 1\n2 3\n3 5\n10 8\n100 1233\n1000000 1145141\n", "output": "1\n3\n4\n14\n153\n1643498\n", "id": 1734420116977}, {"id": 1734420231339, "input": "", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "B1ReverseCardEasyVersion"}}, "batch": {"id": "973c6e40-42a7-489a-8188-8fd76408bda3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_1_Reverse_Card_Easy_Version.cpp"}