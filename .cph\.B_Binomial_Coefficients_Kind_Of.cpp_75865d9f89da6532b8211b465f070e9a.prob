{"name": "<PERSON>. Binomial Coefficients, Kind Of", "group": "Codeforces - Educational Codeforces Round 170 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2025/problem/B", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1729233558227, "input": "7\n2 5 5 100000 100000 100000 100000\n1 2 3 1 33333 66666 99999\n", "output": "2\n4\n8\n2\n326186014\n984426998\n303861760\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BBinomialCoefficientsKindOf"}}, "batch": {"id": "1667cfdc-b7fd-4ead-bba6-0e8d473f5a6d", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Binomial_Coefficients_Kind_Of.cpp"}