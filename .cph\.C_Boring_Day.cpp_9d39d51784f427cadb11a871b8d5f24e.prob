{"name": "<PERSON><PERSON>", "group": "Codeforces - Codeforces Round 955 (Div. 2, with prizes from NEAR!)", "url": "https://codeforces.com/problemset/problem/1982/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720020259910, "input": "8\n5 3 10\n2 1 11 3 7\n10 1 5\n17 8 12 11 7 11 21 13 10 8\n3 4 5\n3 4 2\n8 12 25\n10 7 5 13 8 9 12 7\n2 3 3\n5 2\n9 7 9\n2 10 5 1 3 7 6 2 3\n1 8 10\n9\n5 5 6\n1 4 2 6 4\n", "output": "3\n0\n1\n4\n0\n3\n1\n2\n"}, {"id": 1720024358473, "input": "1\n3 3 3\n1 1 2", "output": "1"}, {"id": 1720024980454, "input": "1\n3 3 3 \n2 2 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CBoringDay"}}, "batch": {"id": "502fbb62-6eda-460a-96e5-a80faf30bd57", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Boring_Day.cpp"}