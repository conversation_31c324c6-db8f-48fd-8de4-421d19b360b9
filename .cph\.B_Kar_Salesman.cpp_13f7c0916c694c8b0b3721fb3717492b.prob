{"name": "<PERSON><PERSON> Salesman", "group": "Codeforces - Codeforces Round 978 (Div. 2)", "url": "https://codeforces.com/problemset/problem/2022/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "4\n3 2\n3 1 2\n3 3\n2 1 3\n5 3\n2 2 1 9 2\n7 4\n2 5 3 3 5 2 5\n", "output": "3\n3\n9\n7\n", "id": 1730132336265}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BKarSalesman"}}, "batch": {"id": "91949ab1-83b3-48a3-8d2a-95187bda328e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Kar_Salesman.cpp"}