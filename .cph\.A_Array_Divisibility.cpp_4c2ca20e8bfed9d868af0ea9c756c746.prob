{"name": "<PERSON><PERSON> Divisibility", "group": "Codeforces - Codeforces Round #956 (Div. 2) and ByteRace 2024", "url": "https://codeforces.com/contest/1983/problem/0", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1720363043703, "input": "3\n3\n6\n7\n", "output": "4 22 18\n10 6 15 32 125 54\n23 18 27 36 5 66 7\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "AArrayDivisibility"}}, "batch": {"id": "7c53174e-ebb2-463d-a261-d51a2365077c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Array_Divisibility.cpp"}