{"name": "<PERSON><PERSON>'s Assignment", "group": "Codeforces - Codeforces Round 981 (Div. 3)", "url": "https://codeforces.com/contest/2033/problem/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1729787636976, "input": "3\n5\n2 1 -3 2 1\n7\n12 -4 4 43 -3 -5 8\n6\n0 -4 0 3 0 1\n", "output": "1\n2\n3\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DKousukesAssignment"}}, "batch": {"id": "e81148dd-5361-4183-b3f0-951a18b6c449", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Kousuke_s_Assignment.cpp"}