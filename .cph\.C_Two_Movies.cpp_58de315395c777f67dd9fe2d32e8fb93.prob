{"name": "C. Two Movies", "group": "Codeforces - Educational Codeforces Round 167 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1989/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n2\n-1 1\n-1 -1\n1\n-1\n-1\n5\n0 -1 1 0 1\n-1 1 0 0 1\n4\n-1 -1 -1 1\n-1 1 1 1\n", "output": "0\n-1\n1\n1\n", "id": 1734242049388}, {"id": 1734242673687, "input": "1\n4\n-1 -1 -1 1\n-1 1 1 1", "output": "1"}, {"id": 1734244782245, "input": "1\n3\n-1 -1 1\n-1 1 1", "output": "0"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CTwoMovies"}}, "batch": {"id": "7c2701c8-f2e9-42ed-874a-15de00a3df38", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Two_Movies.cpp"}