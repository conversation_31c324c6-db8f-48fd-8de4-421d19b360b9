{"name": "<PERSON><PERSON> House", "group": "Codeforces - Codeforces Round 904 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1884/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1718173574901, "input": "6\n1\n1\n2\n01\n3\n010\n5\n10101\n7\n0000111\n12\n001011000110\n", "output": "-1\n1 -1\n0 1 -1\n1 3 -1 -1 -1\n3 6 9 12 -1 -1 -1\n0 2 4 6 10 15 20 -1 -1 -1 -1 -1\n"}, {"id": 1718176157834, "input": "1\n3\n100", "output": "0 0 -1"}, {"id": 1718182229006, "input": "1\n3\n001", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BHauntedHouse"}}, "batch": {"id": "cf7e93b7-c1aa-4001-8a60-c7cb9e3b57eb", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Haunted_House.cpp"}