{"name": "B. Maximum Rounding", "group": "Codeforces - Codeforces Round 891 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1857/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719574012821, "input": "10\n1\n5\n99\n913\n1980\n20444\n20445\n60947\n419860\n40862016542130810467\n", "output": "1\n10\n100\n1000\n2000\n20444\n21000\n100000\n420000\n41000000000000000000\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMaximumRounding"}}, "batch": {"id": "c2a57e49-bbf7-408b-b1ac-7c6b688610ef", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Maximum_Rounding.cpp"}