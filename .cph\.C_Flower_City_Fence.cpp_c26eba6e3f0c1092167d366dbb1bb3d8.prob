{"name": "C. Flower City Fence", "group": "Codeforces - Codeforces Round 894 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1862/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719425171559, "input": "7\n5\n5 4 3 2 1\n3\n3 1 1\n3\n4 2 1\n1\n2\n5\n5 3 3 1 1\n5\n5 5 5 3 3\n2\n6 1\n", "output": "YES\nYES\nNO\nNO\nYES\nYES\nNO\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CFlowerCityFence"}}, "batch": {"id": "431ec2a0-f75a-40ad-a28e-a886914c3bde", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Flower_City_Fence.cpp"}