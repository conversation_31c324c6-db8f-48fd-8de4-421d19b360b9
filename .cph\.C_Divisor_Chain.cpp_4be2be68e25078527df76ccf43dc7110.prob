{"name": "C. Divisor Chain", "group": "Codeforces - Harbour.Space Scholarship Contest 2023-2024 (Div. 1 + Div. 2)", "url": "https://codeforces.com/problemset/problem/1864/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "3\n3\n5\n14\n", "output": "3\n3 2 1\n4\n5 4 2 1\n6\n14 12 6 3 2 1\n", "id": 1733847960243}, {"id": 1733849848673, "input": "122\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n61\n62\n63\n64\n65\n66\n67\n68\n69\n70\n71\n72\n73\n74\n75\n76\n77\n78\n79\n80\n81\n82\n83\n84\n85\n86\n87\n88\n89\n90\n91\n92\n93\n94\n95\n96\n97\n98\n99\n100\n101\n102\n103\n104\n105\n106\n107\n108\n109\n110\n111\n112\n113\n114\n115\n116\n117\n118\n119\n120\n121\n122\n123", "output": ""}, {"id": 1733853934751, "input": "1\n47", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "batch": {"id": "98e03c3c-764c-44f2-a27a-7c08cb597e3f", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Divisor_Chain.cpp"}