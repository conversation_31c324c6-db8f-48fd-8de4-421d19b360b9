{"name": "D. Three Activities", "group": "Codeforces - Codeforces Round 916 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1914/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1720530368589, "input": "4\n3\n1 10 1\n10 1 1\n1 1 10\n4\n30 20 10 1\n30 5 15 20\n30 25 10 10\n10\n5 19 12 3 18 18 6 17 10 13\n15 17 19 11 16 3 11 17 17 17\n1 17 18 10 15 8 17 3 13 12\n10\n17 5 4 18 12 4 11 2 16 16\n8 4 14 19 3 12 6 7 5 16\n3 4 8 11 10 8 10 2 20 3\n", "output": "30\n75\n55\n56\n"}, {"id": 1720531710303, "input": "1\n3\n1 10 1\n10 1 1\n1 1 10\n", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DThreeActivities"}}, "batch": {"id": "0cb1c3c0-d9c3-4f85-b767-e011d1b54358", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Three_Activities.cpp"}