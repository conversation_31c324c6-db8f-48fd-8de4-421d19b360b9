{"name": "C. Beautiful Triple Pairs", "group": "Codeforces - Codeforces Round 946 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1974/C", "interactive": false, "memoryLimit": 256, "timeLimit": 4000, "tests": [{"input": "8\n5\n3 2 2 2 3\n5\n1 2 1 2 1\n8\n1 2 3 2 2 3 4 2\n4\n2 1 1 1\n8\n2 1 1 2 1 1 1 1\n7\n2 1 1 1 1 1 1\n6\n2 1 1 1 1 1\n5\n2 1 1 1 1\n", "output": "2\n0\n3\n1\n8\n4\n3\n2\n", "id": 1734414769366}, {"id": 1734418201300, "input": "1\n8\n2 1 1 2 1 1 1 1", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CBeautifulTriplePairs"}}, "batch": {"id": "85b2365c-60a6-460b-b425-64f5d99032ac", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Beautiful_Triple_Pairs.cpp"}