{"name": "B. <PERSON> and Water", "group": "Codeforces - Codeforces Round 981 (Div. 3)", "url": "https://codeforces.com/contest/2033/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n1\n1\n2\n-1 2\n3 0\n3\n1 2 3\n-2 1 -1\n0 0 -1\n5\n1 1 -1 -1 3\n-3 1 4 4 -4\n-1 -1 3 0 -5\n4 5 3 -3 -1\n3 1 -3 -1 5\n", "output": "0\n1\n4\n19\n", "id": 1729781271374}, {"id": 1729781954484, "input": "1\n5\n1 1 -1 -1 3\n-3 1 4 4 -4\n-1 -1 3 0 -5\n4 5 3 -3 -1\n3 1 -3 -1 5", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BSakurakoAndWater"}}, "batch": {"id": "d84447d1-702b-4521-9bae-ae83bc4f1f6b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Sakurako_and_Water.cpp"}