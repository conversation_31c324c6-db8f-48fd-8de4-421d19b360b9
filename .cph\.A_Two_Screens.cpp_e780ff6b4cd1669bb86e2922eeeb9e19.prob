{"name": "A. Two Screens", "group": "Codeforces - Educational Codeforces Round 170 (Rated for Div. 2)", "url": "https://codeforces.com/contest/2025/problem/0", "interactive": false, "memoryLimit": 512, "timeLimit": 2000, "tests": [{"id": 1728916684749, "input": "3\nGARAGE\nGARAGEFOR<PERSON>LE\nABCDE\nAABCD\nTRAINING\nDRAINING\n", "output": "14\n10\n16\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ATwoScreens"}}, "batch": {"id": "fce836c1-606a-4353-b119-a2d2b6703356", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\A_Two_Screens.cpp"}