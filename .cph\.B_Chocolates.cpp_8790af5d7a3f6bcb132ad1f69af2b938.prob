{"name": "B. Chocolates", "group": "Codeforces - NJACK CP Monthly Contest (August)", "url": "https://codeforces.com/gym/544601/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "10 1 10\n", "output": "-1\n", "id": 1724427792306}, {"id": 1724427792351, "input": "10 6 36\n", "output": "2 8 14 20 26\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BChocolates"}}, "batch": {"id": "794d33ca-07f0-4ae9-ad5c-73fa5c144b63", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Chocolates.cpp"}