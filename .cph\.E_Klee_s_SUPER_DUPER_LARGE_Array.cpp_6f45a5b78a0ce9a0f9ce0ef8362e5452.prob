{"name": "<PERSON><PERSON>'s SUPER DUPER LARGE Array!!!", "group": "Codeforces - Codeforces Round 971 (Div. 4)", "url": "https://codeforces.com/problemset/problem/2009/E", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1734073624261, "input": "4\n2 2\n7 2\n5 3\n1000000000 1000000000\n", "output": "1\n5\n1\n347369930\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "EKleesSUPERDUPERLARGEArray"}}, "batch": {"id": "9f0203a6-2fc9-4488-9099-5f1b98f0e5c5", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\E_Klee_s_SUPER_DUPER_LARGE_Array.cpp"}