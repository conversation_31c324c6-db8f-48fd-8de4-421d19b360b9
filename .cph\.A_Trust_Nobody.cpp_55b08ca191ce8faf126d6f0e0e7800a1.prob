{"name": "<PERSON><PERSON> Nobody", "group": "Codeforces - Codeforces Round 870 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1826/A", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"input": "7\n2\n1 2\n2\n2 2\n2\n0 0\n1\n1\n1\n0\n5\n5 5 3 3 5\n6\n5 3 6 6 3 5\n", "output": "1\n-1\n0\n-1\n0\n3\n4\n", "id": 1722735812840}, {"id": 1722736367665, "input": "1\n9\n0 4 1 1 7 9 8 2 2", "output": "-1"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "ATrustNobody"}}, "batch": {"id": "1e9cd53b-e09d-4944-8e72-9a8dd7fea69e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\A_Trust_Nobody.cpp"}