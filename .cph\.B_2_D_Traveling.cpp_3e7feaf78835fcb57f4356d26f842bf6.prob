{"name": "B. 2D Traveling", "group": "Codeforces - Codeforces Round 896 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1869/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1718291997385, "input": "5\n6 2 3 5\n0 0\n1 -2\n-2 1\n-1 3\n2 -2\n-3 -3\n2 0 1 2\n-1000000000 -1000000000\n1000000000 1000000000\n7 5 4 2\n154 147\n-154 -147\n123 456\n20 23\n43 20\n998 244\n353 100\n3 1 3 1\n0 10\n1 20\n2 30\n4 3 2 4\n0 0\n-100 100\n-1 -1\n-1 0\n", "output": "4\n4000000000\n0\n22\n1\n"}, {"id": 1718421288461, "input": "1\n2 0 1 2\n-1000000000 -1000000000\n1000000000 1000000000", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "B2DTraveling"}}, "batch": {"id": "193e00da-4a89-43ec-85c8-7e8b77546cfb", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_2_D_Traveling.cpp"}