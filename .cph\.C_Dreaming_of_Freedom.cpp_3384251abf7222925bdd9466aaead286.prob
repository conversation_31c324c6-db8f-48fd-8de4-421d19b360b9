{"name": "<PERSON><PERSON> of Freedom", "group": "Codeforces - Codeforces Round 870 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1826/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2500, "tests": [{"input": "5\n3 2\n4 2\n5 3\n1000000 1000000\n1 1000000\n", "output": "YES\nNO\nYES\nNO\nYES\n", "id": 1733466199348}, {"id": 1733466683382, "input": "1\n11 67", "output": "NO"}, {"id": 1733467138214, "input": "4\n15 5\n7 6\n7 8\n1 100", "output": "NO\nYES\nNO\nYES"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CDreamingOfFreedom"}}, "batch": {"id": "912be007-5c17-4bfe-ba6d-3e98410e16b3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Dreaming_of_Freedom.cpp"}