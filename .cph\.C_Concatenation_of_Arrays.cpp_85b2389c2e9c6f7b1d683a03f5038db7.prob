{"name": "<PERSON>. Concatenation of Arrays", "group": "Codeforces - Codeforces Round 980 (Div. 2)", "url": "https://codeforces.com/contest/2024/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"input": "4\n2\n1 4\n2 3\n3\n3 2\n4 3\n2 1\n5\n5 10\n2 3\n9 6\n4 1\n8 7\n1\n10 20\n", "output": "2 3 1 4\n2 1 3 2 4 3\n4 1 2 3 5 10 8 7 9 6\n10 20\n", "id": 1729419351903}, {"id": 1729419672316, "input": "1\n2\n2 4\n2 3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CConcatenationOfArrays"}}, "batch": {"id": "ee79ac22-f5ee-42d1-b18b-4bf9617bc343", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Concatenation_of_Arrays.cpp"}