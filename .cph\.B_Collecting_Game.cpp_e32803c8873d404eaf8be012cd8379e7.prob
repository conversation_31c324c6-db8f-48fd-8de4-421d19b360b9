{"name": "<PERSON><PERSON> Collecting Game", "group": "Codeforces - Codeforces Round 914 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1904/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1717646390119, "input": "4\n5\n20 5 1 4 2\n3\n1434 7 1442\n1\n1\n5\n999999999 999999999 999999999 1000000000 1000000000\n", "output": "4 3 0 3 1\n1 0 2\n0\n4 4 4 4 4\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BCollectingGame"}}, "batch": {"id": "86b39222-191a-4691-aeda-b3572be00edf", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Collecting_Game.cpp"}