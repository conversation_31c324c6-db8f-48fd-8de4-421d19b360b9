{"name": "C. Assembly via Minimums", "group": "Codeforces - Codeforces Round 891 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1857/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1721041232591, "input": "5\n3\n1 3 1\n2\n10\n4\n7 5 3 5 3 3\n5\n2 2 2 2 2 2 2 2 2 2\n5\n3 0 0 -2 0 -2 0 0 -2 -2\n", "output": "1 3 3\n10 10\n7 5 3 12\n2 2 2 2 2\n0 -2 0 3 5\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CAssemblyViaMinimums"}}, "batch": {"id": "e3579d61-f26a-40bd-bf66-e5178b8e913e", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Assembly_via_Minimums.cpp"}