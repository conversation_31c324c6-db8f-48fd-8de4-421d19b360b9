{"name": "<PERSON><PERSON> and <PERSON> Tree", "group": "Codeforces - Codeforces Round 883 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1846/D", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1722050206688, "input": "5\n3 4 2\n1 4 5\n1 5 1\n3\n4 6 6\n1 2 3 4\n2 1 200000\n1 200000\n2 4 3\n9 11\n", "output": "11\n2.5\n34.5\n199999.9999975\n11.333333\n"}, {"id": 1722088763724, "input": "1\n3 4 2\n1 4 5", "output": "11"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DRudolphAndChristmasTree"}}, "batch": {"id": "853f3385-e292-4ba2-8d6e-17b653ee8700", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\D_Rudolph_and_Christmas_Tree.cpp"}