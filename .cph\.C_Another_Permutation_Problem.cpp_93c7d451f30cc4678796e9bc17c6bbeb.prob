{"name": "<PERSON><PERSON> Another Permutation Problem", "group": "Codeforces - Codeforces Round 892 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1859/C", "interactive": false, "memoryLimit": 256, "timeLimit": 3000, "tests": [{"id": 1721038681201, "input": "5\n2\n4\n3\n10\n20\n", "output": "2\n17\n7\n303\n2529\n"}, {"id": 1721039502619, "input": "1\n3", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CAnotherPermutationProblem"}}, "batch": {"id": "a31b6889-f1cd-4549-9225-fa327cb4cf48", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Another_Permutation_Problem.cpp"}