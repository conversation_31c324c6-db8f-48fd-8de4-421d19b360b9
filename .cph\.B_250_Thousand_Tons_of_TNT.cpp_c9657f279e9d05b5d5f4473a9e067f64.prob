{"name": "B. 250 Thousand Tons of TNT", "group": "Codeforces - Codeforces Round 909 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1899/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1717963236739, "input": "5\n2\n1 2\n6\n10 2 3 6 1 3\n4\n1000000000 1000000000 1000000000 1000000000\n15\n60978 82265 78961 56708 39846 31071 4913 4769 29092 91348 64119 72421 98405 222 14294\n8\n19957 69913 37531 96991 57838 21008 14207 19198\n", "output": "1\n9\n0\n189114\n112141\n"}, {"id": 1717965124266, "input": "1\n4\n1000000000 1000000000 1000000000 1000000000", "output": "0"}, {"id": 1717965406800, "input": "1\n4\n100000000 100000000 100000000 100000000", "output": "0"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "B250ThousandTonsOfTNT"}}, "batch": {"id": "23ca3e76-2720-4164-8e80-b1311e38ca74", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_250_Thousand_Tons_of_TNT.cpp"}