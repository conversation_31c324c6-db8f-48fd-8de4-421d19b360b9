{"name": "<PERSON><PERSON> Getting Points", "group": "Codeforces - Educational Codeforces Round 159 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1902/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1717674717546, "input": "5\n1 5 5 2\n14 3000000000 1000000000 500000000\n100 20 1 10\n8 120 10 20\n42 280 13 37\n", "output": "0\n12\n99\n0\n37\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BGettingPoints"}}, "batch": {"id": "118a740f-ab4f-4d9f-b0c8-9eb5c935a07b", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Getting_Points.cpp"}