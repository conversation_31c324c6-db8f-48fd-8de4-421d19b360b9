{"name": "C1. Magnitude (Easy Version)", "group": "Codeforces - Codeforces Global Round 26", "url": "https://codeforces.com/problemset/problem/1984/C1", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1729515686898, "input": "5\n4\n10 -9 -3 4\n8\n1 4 3 4 1 4 3 4\n3\n-1 -2 -3\n4\n-1000000000 1000000000 1000000000 1000000000\n4\n1 9 8 4\n", "output": "6\n24\n6\n4000000000\n22\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "C1MagnitudeEasyVersion"}}, "batch": {"id": "57a9fd9f-7604-4c22-b86a-e31d823d02d4", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_1_Magnitude_Easy_Version.cpp"}