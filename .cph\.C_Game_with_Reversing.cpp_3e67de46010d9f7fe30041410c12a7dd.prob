{"name": "<PERSON><PERSON> with <PERSON><PERSON><PERSON>", "group": "Codeforces - Codeforces Round 879 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1834/C", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1722519437208, "input": "7\n5\nabcde\nabxde\n5\nhello\nolleo\n2\nab\ncd\n7\naaaaaaa\nabbbbba\n1\nq\nq\n6\nyoyoyo\noyoyoy\n8\nabcdefgh\nhguedfbh\n", "output": "1\n2\n3\n9\n0\n2\n6\n"}, {"id": 1722520085242, "input": "1\n3\naab\nabc", "output": "4"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CGameWithReversing"}}, "batch": {"id": "83c9c239-6746-472b-870b-7a9f47d4858c", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Game_with_Reversing.cpp"}