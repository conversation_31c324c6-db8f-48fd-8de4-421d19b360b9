{"name": "B. Binary Path", "group": "Codeforces - Codeforces Round 930 (Div. 2)", "url": "https://codeforces.com/problemset/problem/1937/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1730264629615, "input": "3\n2\n00\n00\n4\n1101\n1100\n8\n00100111\n11101101\n", "output": "000\n2\n11000\n1\n001001101\n4\n"}, {"id": 1730265357565, "input": "1\n3\n010\n000", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "B<PERSON><PERSON>ry<PERSON><PERSON>"}}, "batch": {"id": "098a2745-e5f0-4102-9961-f04d09ff16ae", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Binary_Path.cpp"}