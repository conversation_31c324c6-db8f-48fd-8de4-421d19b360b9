{"name": "C. Alya and Permutation", "group": "Codeforces - Codeforces Global Round 27", "url": "https://codeforces.com/contest/2035/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730041678357, "input": "6\n5\n6\n7\n8\n9\n10\n", "output": "5\n2 1 3 4 5\n7\n1 2 4 6 5 3\n7\n2 4 5 1 3 6 7\n15\n2 4 5 1 3 6 7 8\n9\n2 4 5 6 7 1 3 8 9\n15\n1 2 3 4 5 6 8 10 9 7\n"}, {"id": 1730043936995, "input": "1\n128", "output": ""}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CAlyaAndPermutation"}}, "batch": {"id": "7a1f1a88-ff7a-4f6e-b50c-c56dbf30db88", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Alya_and_Permutation.cpp"}