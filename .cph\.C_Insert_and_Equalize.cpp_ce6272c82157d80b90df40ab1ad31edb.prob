{"name": "<PERSON><PERSON> and Equalize", "group": "Codeforces - Educational Codeforces Round 159 (Rated for Div. 2)", "url": "https://codeforces.com/problemset/problem/1902/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1730543520846, "input": "3\n3\n1 2 3\n5\n1 -19 17 -3 -15\n1\n10\n", "output": "6\n27\n1\n\n"}, {"id": 1730545783982, "input": "1\n3\n-10 -5 5", "output": "6"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CInsertAndEqualize"}}, "batch": {"id": "652f40ae-891f-4bb0-94d0-1afd74090cd5", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\C_Insert_and_Equalize.cpp"}