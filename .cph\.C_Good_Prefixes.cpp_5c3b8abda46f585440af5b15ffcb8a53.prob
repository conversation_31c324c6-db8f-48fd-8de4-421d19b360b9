{"name": "C. Good Prefixes", "group": "Codeforces - Codeforces Round 952 (Div. 4)", "url": "https://codeforces.com/contest/1985/problem/C", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1718116945720, "input": "7\n\n1\n0\n\n1\n1\n\n4\n1 1 2 0\n\n5\n0 1 2 1 4\n\n7\n1 1 0 3 5 2 12\n\n7\n1000000000 1000000000 1000000000 1000000000 1000000000 1000000000 294967296\n10\n0 1000000000 1000000000 1000000000 1000000000 1000000000 1000000000 1000000000 1000000000 589934592\n", "output": "1\n0\n3\n3\n4\n1\n2\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "CGoodPrefixes"}}, "batch": {"id": "05f56c75-13db-4b66-9564-e375588947a7", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\C_Good_Prefixes.cpp"}