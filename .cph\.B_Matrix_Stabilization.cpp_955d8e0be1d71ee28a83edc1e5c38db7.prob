{"name": "B. Matrix Stabilization", "group": "Codeforces - Codeforces Round 954 (Div. 3)", "url": "https://codeforces.com/contest/1986/problem/B", "interactive": false, "memoryLimit": 256, "timeLimit": 2000, "tests": [{"id": 1719154774297, "input": "6\n1 2\n3 1\n2 1\n1\n1\n2 2\n1 2\n3 4\n2 3\n7 4 5\n1 8 10\n5 4\n92 74 31 74\n74 92 17 7\n31 17 92 3\n74 7 3 92\n7 31 1 1\n\n3 3\n1000000000 1 1000000000\n1 1000000000 1\n1000000000 1 1000000000\n", "output": "1 1\n1\n1\n1 2\n3 3\n4 4 5\n1 8 8\n74 74 31 31\n74 74 17 7\n31 17 17 3\n31 7 3 3\n7 7 1 1\n1 1 1\n1 1 1\n1 1 1\n"}, {"id": 1719155896779, "input": "1\n2 2\n1 2\n3 4", "output": "1 2\n3 3"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BMatrixStabilization"}}, "batch": {"id": "510e565f-8e7c-4179-b8a0-a1ba294d8b76", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\C++ files\\B_Matrix_Stabilization.cpp"}