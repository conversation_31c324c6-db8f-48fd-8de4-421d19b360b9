{"name": "B. Fireworks", "group": "Codeforces - Codeforces Round 935 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1945/B", "interactive": false, "memoryLimit": 256, "timeLimit": 1000, "tests": [{"id": 1733676879719, "input": "6\n6 7 4\n3 4 10\n7 8 56\n5 6 78123459896\n1 1 1\n1 1 1000000000000000000\n", "output": "2\n7\n17\n28645268630\n4\n2000000000000000002\n"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "BFireworks"}}, "batch": {"id": "8deb2b43-8708-4625-b148-f4f6e1e0b243", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\B_Fireworks.cpp"}