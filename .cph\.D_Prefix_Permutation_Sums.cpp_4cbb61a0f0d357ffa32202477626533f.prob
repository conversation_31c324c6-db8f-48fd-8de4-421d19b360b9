{"name": "D. Prefix Permutation Sums", "group": "Codeforces - Codeforces Round 888 (Div. 3)", "url": "https://codeforces.com/problemset/problem/1851/D", "interactive": false, "memoryLimit": 256, "timeLimit": 3000, "tests": [{"id": 1733377942565, "input": "12\n5\n6 8 12 15\n5\n1 6 8 15\n4\n1 2 100\n4\n1 3 6\n2\n2\n3\n1 2\n4\n3 7 10\n5\n5 44 46 50\n4\n1 9 10\n5\n13 21 36 42\n5\n1 2 3 1000000000000000000\n9\n9 11 12 20 25 28 30 33\n", "output": "YES\nYES\nNO\nYES\nYES\nNO\nYES\nNO\nNO\nNO\nNO\nNO\n"}, {"id": 1733379157972, "input": "1\n4\n1 3 6", "output": "YES"}, {"id": 1733380560333, "input": "1\n2\n2", "output": "YES"}, {"id": 1733380796860, "input": "1\n5\n3 15 21 22", "output": "NO"}, {"id": 1733381456389, "input": "1\n10\n5 6 10 24 28 29 36 38 41", "output": "NO"}], "testType": "single", "input": {"type": "stdin"}, "output": {"type": "stdout"}, "languages": {"java": {"mainClass": "Main", "taskClass": "DPrefixPermutationSums"}}, "batch": {"id": "022224a7-2853-41e3-b487-540c3f0a40d3", "size": 1}, "srcPath": "c:\\Users\\<USER>\\IdeaProjects\\CP\\D_Prefix_Permutation_Sums.cpp"}